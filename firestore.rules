rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // User profiles: only the user can read/write their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Admin users: only admins can read/write
    match /adminUser/{adminId} {
      allow read, write: if request.auth.token.role in ['admin', 'super_admin'];
    }

    // Public content: anyone can read, only admins can write
    match /news/{docId} {
      allow read: if true;
      allow write: if request.auth.token.role in ['admin', 'super_admin'];
    }
    match /gallery/{docId} {
      allow read: if true;
      allow write: if request.auth.token.role in ['admin', 'super_admin'];
    }
    match /partners/{docId} {
      allow read: if true;
      allow write: if request.auth.token.role in ['admin', 'super_admin'];
    }
    match /rsvps/{docId} {
      allow read: if true;
      allow write: if request.auth.token.role in ['admin', 'super_admin'];
    }

    // TEMP: allow all reads/writes for development
    match /{document=**} {
      allow read, write: if true;
    }
  }
}