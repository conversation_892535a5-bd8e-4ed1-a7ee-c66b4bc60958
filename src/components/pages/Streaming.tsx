'use client'

import { useState, useEffect } from 'react'

const Streaming: React.FC = () => {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  })

  useEffect(() => {
    const targetDate = new Date('2025-08-29T03:30:00-05:00').getTime()

    const timer = setInterval(() => {
      const now = new Date().getTime()
      const difference = targetDate - now

      if (difference > 0) {
        setTimeLeft({
          days: Math.floor(difference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((difference % (1000 * 60)) / 1000)
        })
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  return (
    <section className="py-20 bg-gradient-to-br from-cream via-ivory to-cream relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-20 w-80 h-80 bg-gradient-to-br from-royalGold/10 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tl from-royalBlue/10 to-transparent rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4">
            Royal Coronation Live Stream
          </h1>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Watch the historic coronation of His Majesty King Allen Ellison live from anywhere in the world
          </p>
        </div>

        {/* Live Stream Player */}
        <div
          className="max-w-4xl mx-auto mb-16"
        >
          <div className="relative bg-black rounded-2xl overflow-hidden shadow-2xl">
            <div className="aspect-video bg-gradient-to-br from-royalBlue/20 to-royalGold/20 flex items-center justify-center relative">
              {/* Countdown Timer Overlay */}
              <div className="absolute top-4 right-4 bg-black/70 backdrop-blur-sm rounded-lg p-3 text-white text-sm">
                <div className="flex space-x-2">
                  <span>{timeLeft.days.toString().padStart(2, '0')}d</span>
                  <span>{timeLeft.hours.toString().padStart(2, '0')}h</span>
                  <span>{timeLeft.minutes.toString().padStart(2, '0')}m</span>
                  <span>{timeLeft.seconds.toString().padStart(2, '0')}s</span>
                </div>
              </div>
              
              <div className="text-center text-white">
                <div className="text-6xl mb-4">
                  <i className="fas fa-play-circle"></i>
                </div>
                <h3 className="text-2xl font-bold mb-2">Live Stream Coming Soon</h3>
                <p className="text-lg opacity-80">August 29th, 2025 at 3:30 AM EST</p>
              </div>
            </div>
          </div>
        </div>

        {/* Countdown Section */}
        <div
          className="text-center mb-16"
        >
          <h2 className="text-3xl font-bold text-royalBlue mb-8">Countdown to History</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
            {[
              { label: 'Days', value: timeLeft.days },
              { label: 'Hours', value: timeLeft.hours },
              { label: 'Minutes', value: timeLeft.minutes },
              { label: 'Seconds', value: timeLeft.seconds }
            ].map((item, index) => (
              <div key={item.label} className="bg-white/70 backdrop-blur-lg rounded-xl p-6 border border-white/50 shadow-lg">
                <div className="text-3xl font-bold text-royalGold mb-2">
                  {item.value.toString().padStart(2, '0')}
                </div>
                <div className="text-gray-600 text-sm uppercase tracking-wider">
                  {item.label}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Stream Information */}
        <div
          className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto"
        >
          <div className="bg-white/70 backdrop-blur-lg rounded-xl p-8 border border-white/50 shadow-lg">
            <h3 className="text-xl font-bold text-royalBlue mb-4 flex items-center">
              <i className="fas fa-calendar text-royalGold mr-3"></i>
              Event Details
            </h3>
            <ul className="space-y-3 text-gray-700">
              <li><strong>Date:</strong> August 29th, 2025</li>
              <li><strong>Time:</strong> 3:30 AM EST</li>
              <li><strong>Duration:</strong> Approximately 3 hours</li>
              <li><strong>Language:</strong> English with subtitles</li>
            </ul>
          </div>

          <div className="bg-white/70 backdrop-blur-lg rounded-xl p-8 border border-white/50 shadow-lg">
            <h3 className="text-xl font-bold text-royalBlue mb-4 flex items-center">
              <i className="fas fa-ticket text-royalGold mr-3"></i>
              Access Information
            </h3>
            <ul className="space-y-3 text-gray-700">
              <li><strong>Free Access:</strong> Basic live stream</li>
              <li><strong>Premium Access:</strong> HD quality + extras</li>
              <li><strong>VIP Access:</strong> Multiple camera angles</li>
              <li><strong>Requirements:</strong> Stable internet connection</li>
            </ul>
          </div>
        </div>

        {/* Call to Action */}
        <div
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-royalBlue/10 to-royalGold/10 backdrop-blur-lg rounded-2xl p-12 border border-royalGold/20">
            <h2 className="text-3xl font-bold text-royalBlue mb-4">Don't Miss This Historic Moment</h2>
            <p className="text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
              Set your reminders and join millions around the world as we witness the coronation of His Majesty King Allen Ellison.
            </p>
            <a
              href="/tickets"
              className="inline-block bg-gradient-to-r from-royalGold to-yellow-500 text-white px-8 py-4 rounded-full font-bold text-lg hover:shadow-lg transition-all duration-300 hover:scale-105"
            >
              Get Premium Access
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Streaming
