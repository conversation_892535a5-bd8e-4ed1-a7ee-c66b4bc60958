'use client'

const BiographyPage: React.FC = () => {
  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/5 via-transparent to-royalBlue/5"></div>
      
      {/* Floating background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div
          className="absolute top-20 right-20 w-80 h-80 bg-gradient-to-br from-royalGold/8 to-transparent rounded-full blur-3xl"
        />
        <div
          className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tl from-royalBlue/8 to-transparent rounded-full blur-3xl"
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div
          className="text-center mb-16"
        >
          <h1 className="text-4xl md:text-5xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4">
            Biography of His Royal Majesty
          </h1>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            His Royal Majesty King Allen Ellison, Mpuntuhene of Adukrom - A comprehensive look at the life and legacy of a visionary leader.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-16 items-start max-w-7xl mx-auto">
          {/* Content - Left Side (2/3 width) */}
          <div
            className="lg:col-span-2 space-y-8"
          >
            {/* Full Biography */}
            <div className="bg-white/50 backdrop-blur-lg rounded-xl p-6 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-royalGold to-yellow-500 rounded-lg flex items-center justify-center mr-3">
                  <i className="fas fa-user text-white text-sm"></i>
                </div>
                <h2 className="text-2xl font-bold text-royalBlue">Full Biography</h2>
              </div>

              <div className="space-y-4 text-gray-700 leading-relaxed">
                <p>
                  His Majesty King Allen Ellison, globally known for his visionary leadership, is being enstooled Mpuntuhene of Adukrom, Eastern Region of Ghana—an esteemed royal office dedicated to advancing trade, investment, innovation and economic development for the Kingdom and beyond. A statesman, entrepreneur, humanitarian, and cultural ambassador, King Allen stands at the intersection of African tradition and global transformation.
                </p>
                
                <p>
                  Born in Avon Park, Florida, USA, Allen Ellison has risen from humble beginnings to become one of the most dynamic royal figures of the 21st century. With a Bachelor of Arts degree in Political Science and Business Administration from Florida Southern College, he has spent over two decades driving economic empowerment, financial literacy, and sustainable development across communities in the United States, Asia the Caribbean, and Africa.
                </p>
                
                <p>
                  In 2019, His Majesty was honored with the royal title "Son of the Soil" by His Royal Majesty King Jonathan Danladi Gyet Maude of Nok Kingdom, Nigeria. On August 29, 2025, he will be formally enstooled as Mpuntuhene of Adukrom, a sacred office recognized by the Nifaman Council of the Akuapem State. His coronation will mark a new era of economic leadership, positioning Adukrom as a future-facing Kingdom ready to engage the world through commerce, diplomacy, and innovation.
                </p>
                
                <p>
                  As a Board member of The Bank of Humanity, custodian of the Ellison Royal Sovereign Wealth Fund and Chairman of The Ellison Family Office, His Majesty oversees global initiatives in renewable energy, fintech, education, agriculture, and infrastructure development. His leadership is rooted in the belief that traditional authority can be a powerful force for modern progress—unifying the African diaspora, attracting foreign direct investment, and elevating the continent's narrative on the global stage.
                </p>
                
                <p>
                  King Allen Ellison is also an actor of an award-winning film, published author, businessman, and former U.S. Senatorial candidate. His life and legacy are a living bridge between continents—proof that royalty can be as bold as it is benevolent, as visionary as it is rooted in ancestral truth.
                </p>
              </div>
            </div>

            {/* Purpose Statement */}
            <div className="bg-gradient-to-br from-royalBlue/5 to-royalGold/5 backdrop-blur-lg rounded-xl p-6 border border-royalGold/20 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-royalGold to-yellow-500 rounded-lg flex items-center justify-center mr-3">
                  <i className="fas fa-bullseye text-white text-sm"></i>
                </div>
                <h3 className="text-xl font-bold text-royalBlue">Purpose Statement</h3>
              </div>
              
              <p className="text-gray-700 leading-relaxed italic">
                Our purpose is to preserve tradition while creating opportunity — advancing trade, investment, and diplomacy to uplift communities, bridge Africa with the world, and ignite a lasting legacy of empowerment through The Rebirth of Adukrom.
              </p>
            </div>

            {/* Vision */}
            <div className="bg-white/50 backdrop-blur-lg rounded-xl p-5 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 bg-gradient-to-br from-royalGold to-yellow-500 rounded-lg flex items-center justify-center mr-3">
                  <i className="fas fa-eye text-white text-sm"></i>
                </div>
                <h3 className="text-lg font-bold text-royalBlue">Vision</h3>
              </div>
              <p className="text-gray-700 leading-relaxed">
                To establish Ghana as a beacon of cultural preservation and sustainable development in Africa, where traditional wisdom and modern innovation harmoniously coexist to create prosperity for all citizens.
              </p>
            </div>

            {/* Mission */}
            <div className="bg-white/50 backdrop-blur-lg rounded-xl p-5 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 bg-gradient-to-br from-royalBlue to-blue-600 rounded-lg flex items-center justify-center mr-3">
                  <i className="fas fa-target text-white text-sm"></i>
                </div>
                <h3 className="text-lg font-bold text-royalBlue">Mission</h3>
              </div>
              <p className="text-gray-700 leading-relaxed">
                To lead with wisdom, serve with humility, and govern with justice, ensuring that every citizen has the opportunity to reach their full potential through sustainable development and cultural preservation.
              </p>
            </div>
          </div>

          {/* Profile Image - Right Side (1/3 width) */}
          <div
            className="relative lg:sticky lg:top-24"
          >
            <div className="relative overflow-hidden rounded-2xl shadow-lg group">
              <img
                src="/Website Images/Allen-Ellison-Profile-scaled-1.png"
                alt="His Royal Majesty King Allen Ellison"
                className="w-full h-full object-cover aspect-[3/4] transition-transform duration-500 group-hover:scale-102"
                onError={(e) => {
                  // Fallback if image doesn't exist
                  e.currentTarget.style.display = 'none';
                  const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                  if (nextElement) {
                    nextElement.style.display = 'flex';
                  }
                }}
              />
              {/* Fallback placeholder */}
              <div className="aspect-[3/4] bg-gradient-to-br from-royalBlue/20 to-royalGold/20 flex items-center justify-center" style={{display: 'none'}}>
                <div className="text-4xl text-royalBlue/30">
                  <i className="fas fa-crown"></i>
                </div>
              </div>

              {/* Subtle overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-royalBlue/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              {/* Small royal badge */}
              <div className="absolute top-3 right-3 bg-royalGold/90 backdrop-blur-sm rounded-full p-2 shadow-md">
                <i className="fas fa-crown text-white text-sm"></i>
              </div>
            </div>

            {/* Minimal decorative elements */}
            <div className="absolute -top-1 -right-1 w-8 h-8 bg-gradient-to-br from-royalGold/20 to-yellow-400/20 rounded-full blur-lg"></div>
            <div className="absolute -bottom-1 -left-1 w-10 h-10 bg-gradient-to-tr from-royalBlue/20 to-blue-500/20 rounded-full blur-lg"></div>

            {/* Compact title below image */}
            <div className="mt-4 text-center">
              <h4 className="text-lg font-bold text-royalBlue mb-1">His Royal Majesty</h4>
              <p className="text-royalGold font-semibold text-sm">King Allen Ellison</p>
              <p className="text-gray-600 text-xs mt-1">Mpuntuhene of Adukrom</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default BiographyPage
