'use client'

import React from 'react'
import Header from '../layout/Header'

const Tickets: React.FC = () => {
  return (
    <main className="min-h-screen">
      <Header />
      <div className="relative overflow-hidden bg-gradient-to-br from-royalBlue via-royalBlue/90 to-royalBlue/80 -mt-20">
      {/* Floating background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 right-20 w-80 h-80 bg-gradient-to-br from-royalGold/20 to-transparent rounded-full blur-3xl" />
        <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tl from-royalGold/15 to-transparent rounded-full blur-3xl" />
      </div>

      {/* Hero Section */}
      <section className="py-20 pt-24 relative z-10">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-serif font-bold text-white mb-4 animate-fade-in-up">
              The Crown of Africa – Rise of a New Era
            </h1>
            <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6 animate-scale-in animation-delay-400"></div>

            <h2 className="text-2xl md:text-3xl font-serif font-bold text-royalGold mb-8 animate-slide-in-right animation-delay-600">
              Welcome to the Adukrom Kingdom NFT Collection
            </h2>

            {/* Introduction in boxes */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-6xl mx-auto mb-12">
              <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 scroll-fade-in animate-slide-in-left animation-delay-800">
                <h3 className="text-lg font-bold text-royalGold mb-3">A Living Legacy</h3>
                <p className="text-white/90 text-sm leading-relaxed">
                  Step into a story that spans centuries—a story of kings, queens, warriors, and wisdom. The Adukrom Kingdom, nestled in the heart of Ghana's Eastern Region, is not just a place. It is a living legacy, a guardian of cultural pride, and now, a global symbol of heritage being reborn for a digital generation.
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 scroll-fade-in animate-slide-in-right animation-delay-1000">
                <h3 className="text-lg font-bold text-royalGold mb-3">Bridge to the Future</h3>
                <p className="text-white/90 text-sm leading-relaxed">
                  This groundbreaking NFT collection serves as a bridge between the ancient and the modern, offering a once-in-a-lifetime opportunity to own a piece of royal history while supporting the future of African leadership, education, and cultural empowerment.
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 scroll-fade-in animate-slide-in-left animation-delay-1200">
                <h3 className="text-lg font-bold text-royalGold mb-3">Sacred Traditions</h3>
                <p className="text-white/90 text-sm leading-relaxed">
                  Through art, symbolism, and digital innovation, each NFT unlocks access to a deeper connection with the soul of Adukrom—its stories, festivals, regalia, and the sacred traditions that have held the community together for generations.
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 scroll-fade-in animate-slide-in-right animation-delay-1400">
                <h3 className="text-lg font-bold text-royalGold mb-3">Rise of a New Era</h3>
                <p className="text-white/90 text-sm leading-relaxed">
                  Whether you are a member of the global African diaspora or an ally of African advancement, your participation marks your place in a transformative moment: the Rise of a New Era.
                </p>
              </div>
            </div>

            <div className="bg-gradient-to-r from-royalGold/20 to-yellow-400/20 backdrop-blur-lg rounded-xl p-6 border border-royalGold/30 max-w-4xl mx-auto">
              <div className="text-lg font-semibold text-white space-y-2">
                <p>This is more than a collectible.</p>
                <p>This is a coronation of identity, a celebration of belonging, and a call to rise with the Crown of Africa.</p>
              </div>

              <p className="text-lg font-medium text-royalGold mt-4">
                Welcome to Adukrom Kingdom—where the past meets the future, and tradition becomes legacy on the blockchain.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Process Steps Section */}
      <section className="py-16 relative z-10">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12 scroll-fade-in">
            <h2 className="text-3xl md:text-4xl font-serif font-bold text-white mb-4 animate-fade-in-up">
              How to Get Your Ticket
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6 animate-scale-in animation-delay-200"></div>
            <p className="text-white/90 max-w-2xl mx-auto animate-fade-in-up animation-delay-400">
              Follow these simple steps to secure your place in history
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 max-w-5xl mx-auto">
            {[
              {
                step: "1",
                title: "Choose Your Ticket",
                description: "Select from Gold, Silver, or Bronze packages based on your preferred experience",
                icon: "fas fa-ticket-alt"
              },
              {
                step: "2",
                title: "Create Tiike Account",
                description: "Sign up on Tiike platform with your email and create a secure account",
                icon: "fas fa-user-plus"
              },
              {
                step: "3",
                title: "Pay with Card",
                description: "Complete your purchase using credit or debit card through our secure payment system",
                icon: "fas fa-credit-card"
              },
              {
                step: "4",
                title: "You're Ready!",
                description: "Receive confirmation and access details. Merchandise takes 4-6 weeks to arrive",
                icon: "fas fa-check-circle"
              }
            ].map((item, index) => (
              <div
                key={item.step}
                className={`bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 shadow-lg hover:shadow-xl hover:scale-105 hover:-translate-y-2 transition-all duration-300 text-center scroll-fade-in animate-bounce-in animation-delay-${600 + index * 200}`}
              >
                <div className="w-16 h-16 bg-gradient-to-br from-royalGold to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4 animate-rotate-in animation-delay-800">
                  <i className={`${item.icon} text-white text-xl`}></i>
                </div>
                <div className="text-2xl font-bold text-royalGold mb-2">Step {item.step}</div>
                <h3 className="text-lg font-bold text-white mb-3">{item.title}</h3>
                <p className="text-white/80 text-sm leading-relaxed">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* NFT Tickets Section */}
      <section className="py-20 relative z-10">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16 scroll-fade-in">
            <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-4 animate-fade-in-up">
              NFT Tickets
            </h2>
            <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6 animate-scale-in animation-delay-200"></div>
            <p className="text-lg text-white/90 max-w-3xl mx-auto leading-relaxed animate-fade-in-up animation-delay-400">
              Choose your royal experience and secure your place in history
            </p>
          </div>

          {/* NFT Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {[
              {
                name: 'Gold Ticket Package',
                subtitle: 'The Royal Circle',
                price: '$2,000',
                status: 'Limited',
                image: '/nft/gold-coronation-nft.png',
                features: [
                  'Gold NFT Ticket (collectible + access pass)',
                  'Framed Royal Portrait (numbered & signed)',
                  'Engraved Gold Pen (coronation seal)',
                  'Commemorative Booklet (history & vision)',
                  'Premium Embroidered T-shirt',
                  'Royal NF C Tag',
                  'Limited-Edition Hat or Cap',
                  'VIP Badge & Lanyard',
                  'Access to Post-Coronation Reception',
                  'Blockchain Certificate of Attendance',
                  'Premium Royal Box (collectible packaging)'
                ],
                buttonText: 'Buy Gold Ticket',
                link: 'https://tiike.com/events/gold-nft'
              },
              {
                name: 'Silver Ticket Package',
                subtitle: 'The Noble Watchers',
                price: '$750',
                status: 'Limited',
                image: '/nft/silver-coronation-nft.png',
                features: [
                  'Silver NFT Ticket (stream access + collectible)',
                  'Mailed Postcard (with photo & thank-you)',
                  'Digital Coronation Booklet (interactive PDF)',
                  'Branded Cap or Pin (shipped)',
                  'Royal NFC Tag',
                  'Silver Pen with Logo',
                  'Online Q&A Session with Prince Allen',
                  'Behind-the-Scenes Video Access',
                  'Digital Certificate of Attendance',
                  'Optional Add-On: Physical Commemorative Item ($25)'
                ],
                buttonText: 'Buy Silver Ticket',
                link: 'https://tiike.com/events/silver-nft',
                popular: true
              },
              {
                name: 'Bronze Ticket Package',
                subtitle: 'Royal Stream Pass',
                price: '$50',
                status: '',
                image: '/nft/bronz-coronation-nft.png',
                features: [
                  'Bronze NFT Ticket (livestream + collectible)',
                  'Downloadable Official Photo Pack',
                  'Royal NFC Tag',
                  'Digital Program Booklet (schedule & details)',
                  'Blockchain Certificate of Attendance',
                  'Access to Private Online Watch Party'
                ],
                buttonText: 'Buy Bronze Ticket',
                link: 'https://tiike.com/events/bronze-nft'
              }
            ].map((ticket, index) => (
              <div
                key={ticket.name}
                className={`relative bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 shadow-lg hover:shadow-xl hover:scale-105 hover:-translate-y-2 transition-all duration-300 flex flex-col h-full scroll-fade-in animate-scale-in animation-delay-${600 + index * 200} ${
                  ticket.popular ? 'ring-2 ring-royalGold' : ''
                }`}
              >
                {ticket.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-royalGold text-white px-4 py-1 rounded-full text-xs font-bold">
                    Most Popular
                  </div>
                )}

                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-white mb-1">{ticket.name}</h3>
                  <p className="text-royalGold font-semibold text-sm mb-4">{ticket.subtitle}</p>

                  <img
                    src={ticket.image}
                    alt={ticket.name}
                    className="w-48 h-48 mx-auto mb-4 rounded-xl shadow-lg object-cover"
                  />

                  <div className="mb-4">
                    <div className="text-2xl font-bold text-royalGold mb-1">Price: {ticket.price}</div>
                    {ticket.status && <div className="text-white/80 text-sm font-medium">{ticket.status}</div>}
                  </div>
                </div>

                <div className="flex-grow">
                  <ul className="space-y-2 mb-6">
                    {ticket.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start text-white/90 text-sm">
                        <span className="mr-2 mt-0.5 text-royalGold">•</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mt-auto">
                  <a
                    href={ticket.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block w-full bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue text-center py-3 rounded-xl font-bold hover:shadow-lg transition-all duration-300 hover:scale-105"
                  >
                    {ticket.buttonText}
                  </a>
                </div>
              </div>
          ))}
        </div>

        {/* Optional Add-Ons Section */}
        <div className="text-center mt-16">
          <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 shadow-lg hover:shadow-xl transition-all duration-300 max-w-2xl mx-auto">
            <div className="flex items-center justify-center mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-royalGold to-yellow-500 rounded-lg flex items-center justify-center mr-3">
                <i className="fas fa-plus text-white text-sm"></i>
              </div>
              <h3 className="text-xl font-bold text-white">Optional Add-Ons</h3>
            </div>
            <ul className="space-y-3 text-white/90">
              <li className="flex justify-between items-center">
                <span>Commemorative Video Download:</span>
                <span className="text-royalGold font-bold">$20</span>
              </li>
              <li className="flex justify-between items-center">
                <span>Merch Bundle Upgrade (Silver/Bronze):</span>
                <span className="text-royalGold font-bold">$40-$75</span>
              </li>
              <li className="flex justify-between items-center">
                <span>NFT Art Poster Print:</span>
                <span className="text-royalGold font-bold">$35</span>
              </li>
            </ul>
            <div className="mt-6 p-4 bg-royalGold/20 rounded-lg border border-royalGold/30">
              <p className="text-white/90 text-sm">
                <i className="fas fa-info-circle text-royalGold mr-2"></i>
                On the day of the stream, receive a unique pin code for secure access (one-time use).
              </p>
              <p className="text-white/80 text-sm mt-2">
                <i className="fas fa-shipping-fast text-royalGold mr-2"></i>
                Merchandise takes 4-6 weeks to arrive.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    {/* Embrace the Legacy Section */}
    <section className="py-20 relative z-10">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-4">
            Embrace the Legacy
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-8"></div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto mb-12">
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 shadow-lg">
              <h3 className="text-lg font-bold text-royalGold mb-3">Join the Movement</h3>
              <p className="text-white/90 text-sm leading-relaxed">
                By acquiring an NFT from the Adukrom Kingdom Collection, you're doing more than collecting a rare digital asset — you're joining a movement. You become part of a global community bound by purpose: preserving history, advancing education, and celebrating the enduring strength of African identity.
              </p>
            </div>

            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 shadow-lg">
              <h3 className="text-lg font-bold text-royalGold mb-3">Powerful Mission</h3>
              <p className="text-white/90 text-sm leading-relaxed">
                Your support fuels a powerful mission to keep the legacy of Adukrom alive — not just in memory, but in action. Funds raised go directly toward cultural preservation projects, youth empowerment programs, and initiatives that project Ghana's royal traditions onto the global stage.
              </p>
            </div>
          </div>

          <div className="mt-12">
            <img
              src="/nftimages/royalretreat.png"
              alt="Royal Retreat"
              className="w-full max-w-2xl mx-auto rounded-xl shadow-lg"
            />
          </div>
        </div>
      </div>
    </section>

    {/* The Living Legacy Section */}
    <section className="py-20 relative z-10">
      <div className="container mx-auto px-4">
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-4">
              The Living Legacy of the Adukrom Kingdom
            </h2>
            <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto"></div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center max-w-6xl mx-auto">
            <div className="order-2 lg:order-1">
              <div className="relative overflow-hidden rounded-xl shadow-lg">
                <img
                  src="/nftimages/princeellison.png"
                  alt="Prince Ellison"
                  className="w-full max-w-md mx-auto rounded-xl shadow-lg object-cover"
                />
                <div className="absolute top-3 right-3 bg-royalGold/90 backdrop-blur-sm rounded-full p-2 shadow-md">
                  <i className="fas fa-crown text-white text-sm"></i>
                </div>
              </div>
            </div>

            <div className="order-1 lg:order-2">
              <div className="space-y-6">
                <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 shadow-lg">
                  <h3 className="text-lg font-bold text-royalGold mb-3">Ancient Foundations</h3>
                  <p className="text-white/90 text-sm leading-relaxed">
                    Rooted in the lush hills of Ghana's Eastern Region, the Adukrom Kingdom is a living testament to bravery, resilience, and spiritual depth. Its story begins in the 16th century, when settlers like Opanyin Kokora Boame and Opanyin Adu laid the foundation of what would become "Adu's town" — Adukrom.
                  </p>
                </div>

                <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 shadow-lg">
                  <h3 className="text-lg font-bold text-royalGold mb-3">Guardian of Identity</h3>
                  <p className="text-white/90 text-sm leading-relaxed">
                    Through centuries of leadership—from military victories like the Akatamanso War, to heroic acts in the Awuna War, to spiritual and administrative continuity under the sacred Otutu Stool—Adukrom has remained a guardian of Ghanaian identity.
                  </p>
                </div>

                <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/20 shadow-lg">
                  <h3 className="text-lg font-bold text-royalGold mb-3">Modern Renaissance</h3>
                  <p className="text-white/90 text-sm leading-relaxed">
                    Now under the guidance of Osuodumgya Otutu Ababio V, Adukrom is experiencing a modern renaissance. This movement reached a monumental milestone with the 2025 Grand Coronation of His Majesty Mpuntuhene Allen Ellison, enstooled as the King of Trade, Investment, and Economic Development.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
      </div>
    </main>
  )
}

export default Tickets
