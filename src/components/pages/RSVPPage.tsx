'use client'
import { useState } from 'react'

const RSVPPage: React.FC = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    attendanceType: '',
    guestCount: '1',
    dietaryRestrictions: '',
    message: ''
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log('RSVP Form submitted:', formData)
    // You can add actual form submission logic here
  }

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/5 via-transparent to-royalBlue/5"></div>
      
      {/* Floating background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div
          className="absolute top-20 right-20 w-80 h-80 bg-gradient-to-br from-royalGold/8 to-transparent rounded-full blur-3xl"
        />
        <div
          className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tl from-royalBlue/8 to-transparent rounded-full blur-3xl"
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div
          className="text-center mb-16 scroll-fade-in"
        >
          <h1 className="text-4xl md:text-5xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4 animate-fade-in-up">
            Royal Coronation RSVP
          </h1>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6 animate-scale-in animation-delay-400"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed animate-fade-in-up animation-delay-600">
            Join us for the historic coronation of His Majesty King Allen Ellison on August 29th, 2025 at 3:30 AM EST
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start max-w-6xl mx-auto">
          {/* RSVP Form - Left Side */}
          <div className="scroll-slide-left"
          >
            <div className="bg-white/50 backdrop-blur-lg rounded-xl p-8 border border-white/30 shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 animate-slide-in-left animation-delay-800">
              <div className="flex items-center mb-6">
                <div className="w-8 h-8 bg-gradient-to-br from-royalGold to-yellow-500 rounded-lg flex items-center justify-center mr-3">
                  <i className="fas fa-calendar-check text-white text-sm"></i>
                </div>
                <h2 className="text-2xl font-bold text-royalBlue">Reserve Your Place</h2>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                      First Name *
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-royalGold focus:border-transparent transition-all duration-300"
                      placeholder="Enter your first name"
                    />
                  </div>
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-royalGold focus:border-transparent transition-all duration-300"
                      placeholder="Enter your last name"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-royalGold focus:border-transparent transition-all duration-300"
                    placeholder="Enter your email address"
                  />
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-royalGold focus:border-transparent transition-all duration-300"
                    placeholder="Enter your phone number"
                  />
                </div>

                <div>
                  <label htmlFor="attendanceType" className="block text-sm font-medium text-gray-700 mb-2">
                    Attendance Type *
                  </label>
                  <select
                    id="attendanceType"
                    name="attendanceType"
                    value={formData.attendanceType}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-royalGold focus:border-transparent transition-all duration-300"
                  >
                    <option value="">Select attendance type</option>
                    <option value="in-person">In-Person Attendance</option>
                    <option value="virtual">Virtual Attendance</option>
                    <option value="both">Both In-Person and Virtual</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="guestCount" className="block text-sm font-medium text-gray-700 mb-2">
                    Number of Guests
                  </label>
                  <select
                    id="guestCount"
                    name="guestCount"
                    value={formData.guestCount}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-royalGold focus:border-transparent transition-all duration-300"
                  >
                    <option value="1">1 Guest (Just me)</option>
                    <option value="2">2 Guests</option>
                    <option value="3">3 Guests</option>
                    <option value="4">4 Guests</option>
                    <option value="5+">5+ Guests</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="dietaryRestrictions" className="block text-sm font-medium text-gray-700 mb-2">
                    Dietary Restrictions
                  </label>
                  <input
                    type="text"
                    id="dietaryRestrictions"
                    name="dietaryRestrictions"
                    value={formData.dietaryRestrictions}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-royalGold focus:border-transparent transition-all duration-300"
                    placeholder="Any dietary restrictions or allergies"
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    Special Message
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-royalGold focus:border-transparent transition-all duration-300"
                    placeholder="Any special message or requests"
                  />
                </div>

                <button
                  type="submit"
                  className="w-full bg-gradient-to-r from-royalGold to-yellow-500 text-white py-4 rounded-lg font-bold text-lg hover:shadow-lg transition-all duration-300"
                >
                  Submit RSVP
                </button>
              </form>
            </div>
          </div>

          {/* Event Information - Right Side */}
          <div
            className="space-y-6 scroll-slide-right"
          >
            {/* Event Details */}
            <div className="bg-white/50 backdrop-blur-lg rounded-xl p-6 border border-white/30 shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 animate-slide-in-right animation-delay-1000">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-royalBlue to-blue-600 rounded-lg flex items-center justify-center mr-3">
                  <i className="fas fa-crown text-white text-sm"></i>
                </div>
                <h3 className="text-xl font-bold text-royalBlue">Event Details</h3>
              </div>
              
              <div className="space-y-3 text-gray-700">
                <div className="flex items-center">
                  <i className="fas fa-calendar text-royalGold mr-3 w-4"></i>
                  <span><strong>Date:</strong> August 29th, 2025</span>
                </div>
                <div className="flex items-center">
                  <i className="fas fa-clock text-royalGold mr-3 w-4"></i>
                  <span><strong>Time:</strong> 3:30 AM EST</span>
                </div>
                <div className="flex items-center">
                  <i className="fas fa-map-marker-alt text-royalGold mr-3 w-4"></i>
                  <span><strong>Location:</strong> Adukrom Kingdom, Eastern Region, Ghana</span>
                </div>
                <div className="flex items-center">
                  <i className="fas fa-users text-royalGold mr-3 w-4"></i>
                  <span><strong>Dress Code:</strong> Formal/Traditional Attire</span>
                </div>
              </div>
            </div>

            {/* What to Expect */}
            <div className="bg-gradient-to-br from-royalBlue/5 to-royalGold/5 backdrop-blur-lg rounded-xl p-6 border border-royalGold/20 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-royalGold to-yellow-500 rounded-lg flex items-center justify-center mr-3">
                  <i className="fas fa-star text-white text-sm"></i>
                </div>
                <h3 className="text-xl font-bold text-royalBlue">What to Expect</h3>
              </div>
              
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start">
                  <i className="fas fa-check text-royalGold mr-3 mt-1"></i>
                  <span>Traditional coronation ceremony</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check text-royalGold mr-3 mt-1"></i>
                  <span>Cultural performances and music</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check text-royalGold mr-3 mt-1"></i>
                  <span>Royal feast and refreshments</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check text-royalGold mr-3 mt-1"></i>
                  <span>Meet and greet with dignitaries</span>
                </li>
                <li className="flex items-start">
                  <i className="fas fa-check text-royalGold mr-3 mt-1"></i>
                  <span>Live streaming for virtual attendees</span>
                </li>
              </ul>
            </div>

            {/* Contact Information */}
            <div className="bg-white/50 backdrop-blur-lg rounded-xl p-6 border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-royalBlue to-blue-600 rounded-lg flex items-center justify-center mr-3">
                  <i className="fas fa-envelope text-white text-sm"></i>
                </div>
                <h3 className="text-xl font-bold text-royalBlue">Need Help?</h3>
              </div>
              
              <p className="text-gray-700 text-sm leading-relaxed">
                For questions about the event or assistance with your RSVP, please contact our event coordination team.
              </p>
              
              <div className="mt-4 space-y-2 text-sm text-gray-600">
                <div className="flex items-center">
                  <i className="fas fa-envelope text-royalGold mr-2"></i>
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center">
                  <i className="fas fa-phone text-royalGold mr-2"></i>
                  <span>+233 (0) 123 456 789</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default RSVPPage
