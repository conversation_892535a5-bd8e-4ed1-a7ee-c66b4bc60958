'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { getImageUrl } from '../../utils/imageConfig'
import { GalleryService, GalleryImage } from '../../services/galleryService'

const RoyalGalleryPage: React.FC = () => {
  const [images, setImages] = useState<GalleryImage[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null)
  const [loading, setLoading] = useState(true)

  // Mock data - will be replaced with Firebase data
  const mockImages: GalleryImage[] = [
    {
      id: '1',
      title: 'Royal Palace',
      description: 'The majestic Royal Palace in all its grandeur',
      imagePath: 'gallery/palace.jpg',
      category: 'palace',
      uploadedAt: '2024-01-15',
      featured: true
    },
    {
      id: '2',
      title: 'Royal Coronation Ceremony',
      description: 'Historic moments from the royal coronation ceremony',
      imagePath: 'gallery/coronation1.jpg',
      category: 'royal-events',
      uploadedAt: '2024-01-14',
      featured: true
    },
    {
      id: '3',
      title: 'Traditional Ceremony',
      description: 'Sacred traditional ceremonies and rituals',
      imagePath: 'gallery/ceremony.jpg',
      category: 'ceremonies',
      uploadedAt: '2024-01-13',
      featured: false
    },
    {
      id: '4',
      title: 'Community Gathering',
      description: 'Royal engagement with the local community',
      imagePath: 'gallery/community.jpg',
      category: 'community',
      uploadedAt: '2024-01-12',
      featured: false
    },
    {
      id: '5',
      title: 'Traditional Dance',
      description: 'Cultural dance performances and celebrations',
      imagePath: 'gallery/traditional-dance.jpg',
      category: 'culture',
      uploadedAt: '2024-01-11',
      featured: false
    },
    {
      id: '6',
      title: 'Royal Artifacts',
      description: 'Precious artifacts and royal treasures',
      imagePath: 'gallery/artifacts.jpg',
      category: 'culture',
      uploadedAt: '2024-01-10',
      featured: false
    }
  ]

  useEffect(() => {
    const loadImages = async () => {
      setLoading(true)
      try {
        const galleryImages = await GalleryService.getAllImages()
        setImages(galleryImages)
      } catch (error) {
        console.error('Error loading gallery images:', error)
        // Fallback to mock data if service fails
        setImages(mockImages)
      } finally {
        setLoading(false)
      }
    }

    loadImages()
  }, [])
  // Categories for filtering
  const categories = [
    { id: 'all', name: 'All Images', icon: 'fas fa-images' },
    { id: 'royal-events', name: 'Royal Events', icon: 'fas fa-crown' },
    { id: 'ceremonies', name: 'Ceremonies', icon: 'fas fa-church' },
    { id: 'community', name: 'Community', icon: 'fas fa-users' },
    { id: 'culture', name: 'Culture & Heritage', icon: 'fas fa-palette' },
    { id: 'palace', name: 'Palace & Architecture', icon: 'fas fa-building' }
  ]

  const filteredImages = selectedCategory === 'all'
    ? images
    : images.filter(img => img.category === selectedCategory)

  const openLightbox = (image: GalleryImage) => {
    setSelectedImage(image)
  }

  const closeLightbox = () => {
    setSelectedImage(null)
  }

  const featuredImage = {
    title: 'His Royal Majesty in Traditional Regalia',
    description: 'A magnificent portrait of His Royal Majesty wearing the traditional royal regalia, symbolizing the rich heritage and dignity of the Ghanaian monarchy.',
    category: 'Royal Portrait'
  }

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/20 via-royalGold/5 to-royalGold/15"></div>
      <div className="absolute inset-0 bg-gradient-to-bl from-transparent via-royalGold/3 to-royalGold/8"></div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-4">
            Royal Gallery
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-white/90 max-w-3xl mx-auto leading-relaxed">
            Explore the visual heritage of the Kingdom through our comprehensive collection of photographs, artwork, and historical documentation.
          </p>
        </div>

        {/* Featured Image */}
        <div
          className="mb-20"
        >
          <div className="bg-white/10 backdrop-blur-lg rounded-3xl overflow-hidden border border-white/20 shadow-2xl">
            <div className="md:flex">
              <div className="md:w-1/2">
                <div className="h-96 bg-gradient-to-br from-royalGold/30 to-royalBlue/30 flex items-center justify-center">
                  <i className="fas fa-crown text-8xl text-royalGold"></i>
                </div>
              </div>
              <div className="md:w-1/2 p-8 flex items-center">
                <div>
                  <span className="px-3 py-1 bg-royalGold/20 text-royalGold text-sm font-semibold rounded-full mb-4 inline-block">
                    {featuredImage.category}
                  </span>
                  <h3 className="text-2xl md:text-3xl font-serif font-bold text-white mb-4">
                    {featuredImage.title}
                  </h3>
                  <p className="text-white/80 leading-relaxed mb-6">
                    {featuredImage.description}
                  </p>
                  <button
                    className="px-6 py-3 relative overflow-hidden text-royalBlue font-bold rounded-xl shadow-lg border border-yellow-300 group"
                    style={{
                      background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
                      boxShadow: '0 8px 32px rgba(255, 215, 0, 0.4)'
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
                    <span className="relative z-10 font-extrabold">
                      <i className="fas fa-expand mr-2"></i>
                      View Full Size
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Category Filter */}
        <div className="mb-12">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 flex items-center gap-2 ${
                  selectedCategory === category.id
                    ? 'bg-royalGold text-royalBlue shadow-lg'
                    : 'bg-white/10 text-white hover:bg-white/20'
                }`}
              >
                <i className={category.icon}></i>
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Gallery Grid */}
        <div className="mb-16">
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[...Array(8)].map((_, index) => (
                <div key={index} className="bg-white/10 rounded-2xl h-64 animate-pulse"></div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredImages.map((image) => (
                <div
                  key={image.id}
                  className="group relative cursor-pointer"
                  onClick={() => openLightbox(image)}
                >
                  <div className="bg-white/10 backdrop-blur-lg rounded-2xl overflow-hidden border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 h-64">
                    <div className="relative h-full">
                      <Image
                        src={getImageUrl(image.imagePath)}
                        alt={image.title}
                        fill
                        className="object-cover object-center transition-transform duration-300 group-hover:scale-105"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                        quality={90}
                      />

                      {/* Featured Badge */}
                      {image.featured && (
                        <div className="absolute top-4 left-4 bg-royalGold text-royalBlue px-3 py-1 rounded-full text-xs font-bold">
                          Featured
                        </div>
                      )}

                      {/* Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                      {/* Content */}
                      <div className="absolute bottom-0 left-0 right-0 p-4 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                        <h3 className="font-bold text-lg mb-1 drop-shadow-lg">{image.title}</h3>
                        <p className="text-sm text-white/90 drop-shadow-md line-clamp-2">{image.description}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {filteredImages.length === 0 && !loading && (
            <div className="text-center py-16">
              <i className="fas fa-images text-6xl text-white/40 mb-4"></i>
              <h3 className="text-2xl font-bold text-white mb-2">No Images Found</h3>
              <p className="text-white/70">No images available in this category yet.</p>
            </div>
          )}
        </div>

        {/* Gallery Stats */}
        <div
          className="mt-20"
        >
          <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-3xl font-bold text-royalGold mb-2">500+</div>
                <div className="text-white/80 text-sm">Royal Photographs</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-royalGold mb-2">50+</div>
                <div className="text-white/80 text-sm">Historic Events</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-royalGold mb-2">25+</div>
                <div className="text-white/80 text-sm">Years Documented</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-royalGold mb-2">100+</div>
                <div className="text-white/80 text-sm">Cultural Artifacts</div>
              </div>
            </div>
          </div>
        </div>

        {/* Download Gallery */}
        <div
          className="text-center mt-16"
        >
          <h3 className="text-2xl font-serif font-bold text-white mb-4">Download High-Resolution Images</h3>
          <p className="text-white/80 mb-8 max-w-2xl mx-auto">
            Access our complete collection of high-resolution royal photographs for media, educational, and research purposes.
          </p>
          <button
            className="px-8 py-3 relative overflow-hidden text-royalBlue font-bold rounded-xl shadow-2xl border-2 border-yellow-300 group"
            style={{
              background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
              boxShadow: '0 12px 40px rgba(255, 215, 0, 0.5)'
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out"></div>
            <span className="relative z-10 font-extrabold">
              <i className="fas fa-download mr-2"></i>
              Request Media Kit
            </span>
          </button>
        </div>
      </div>

      {/* Lightbox Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
          onClick={closeLightbox}
        >
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={closeLightbox}
              className="absolute -top-12 right-0 text-white hover:text-royalGold transition-colors text-2xl"
            >
              <i className="fas fa-times"></i>
            </button>

            <div className="relative">
              <Image
                src={getImageUrl(selectedImage.imagePath)}
                alt={selectedImage.title}
                width={800}
                height={600}
                className="max-w-full max-h-[80vh] object-contain rounded-lg"
                quality={95}
              />

              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6 rounded-b-lg">
                <h3 className="text-2xl font-bold text-white mb-2">{selectedImage.title}</h3>
                <p className="text-white/90 mb-2">{selectedImage.description}</p>
                <p className="text-white/70 text-sm">
                  Uploaded: {new Date(selectedImage.uploadedAt).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  )
}

export default RoyalGalleryPage
