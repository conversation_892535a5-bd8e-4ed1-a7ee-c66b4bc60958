'use client'

import React from 'react'
import Header from '../layout/Header'

const Events: React.FC = () => {
  const upcomingEvents = [
    {
      title: 'Royal Coronation Ceremony',
      date: 'August 29, 2025',
      time: '3:30 AM EST',
      location: 'Adukrom Palace, Ghana',
      description: 'The historic coronation ceremony of His Royal Majesty, marking the beginning of a new era for the Kingdom.',
      category: 'Royal Ceremony',
      status: 'Upcoming',
      image: '/api/placeholder/400/250',
      ticketsAvailable: true
    },
    {
      title: 'Royal Gala Dinner',
      date: 'August 30, 2025',
      time: '8:30 AM EST',
      location: 'Grand Ballroom, Accra',
      description: 'An elegant evening of celebration featuring traditional performances, fine dining, and networking with distinguished guests.',
      category: 'Royal Event',
      status: 'Upcoming',
      image: '/api/placeholder/400/250',
      ticketsAvailable: true
    },
    {
      title: 'Global Economic Forum',
      date: 'August 31, 2025',
      time: '10:00 PM EST',
      location: 'Adukrom Convention Center',
      description: 'A forward-looking conference bringing together investors, entrepreneurs, and leaders to explore economic opportunities and partnerships with the Kingdom.',
      category: 'Business',
      status: 'Upcoming',
      image: '/api/placeholder/400/250',
      ticketsAvailable: false
    },
    {
      title: 'Youth Leadership Forum',
      date: 'September 5, 2025',
      time: '11:00 AM EST',
      location: 'Royal Academy',
      description: 'Empowering the next generation of leaders through mentorship and educational programs.',
      category: 'Education',
      status: 'Upcoming',
      image: '/api/placeholder/400/250',
      ticketsAvailable: true
    }
  ]

  const pastEvents = [
    {
      title: 'Royal Charity Gala',
      date: 'December 10, 2024',
      location: 'Royal Ballroom',
      description: 'A successful charity gala raising funds for education initiatives across Ghana.',
      category: 'Charity',
      status: 'Completed',
      image: '/api/placeholder/400/250'
    },
    {
      title: 'Traditional Arts Exhibition',
      date: 'November 15, 2024',
      location: 'Royal Museum',
      description: 'Showcasing the finest traditional arts and crafts from across the Kingdom.',
      category: 'Cultural',
      status: 'Completed',
      image: '/api/placeholder/400/250'
    }
  ]

  return (
    <main className="min-h-screen">
      <Header />
      <section className="py-20 pt-20 relative overflow-hidden -mt-20">
          {/* Background with gradients */}
          <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
          <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/20 via-royalGold/5 to-royalGold/15"></div>
          <div className="absolute inset-0 bg-gradient-to-bl from-transparent via-royalGold/3 to-royalGold/8"></div>

          {/* Background pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="royal-pattern w-full h-full"></div>
          </div>

          {/* Background elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-20 right-20 w-80 h-80 bg-gradient-to-br from-royalGold/20 to-transparent rounded-full blur-3xl"></div>
            <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tl from-white/15 to-transparent rounded-full blur-2xl"></div>
          </div>

          <div className="container mx-auto px-4 relative z-10 pt-24">
            <div className="text-center mb-16">
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-serif font-bold text-white mb-6 leading-tight">
                <span className="block animate-slide-in-left animation-delay-200">Royal Events</span>
                <span className="block text-royalGold text-2xl md:text-3xl lg:text-4xl mt-4 font-light animate-slide-in-right animation-delay-400">
                  Celebrating Our Kingdom's Legacy
                </span>
              </h1>
              <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6 animate-scale-in animation-delay-600"></div>
              <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-4xl mx-auto leading-relaxed animate-fade-in-up animation-delay-800">
                Join us for exclusive royal events, cultural celebrations, and meaningful gatherings that shape our Kingdom's future and honor our rich heritage.
              </p>
            </div>

            {/* Upcoming Events */}
            <div className="mb-20 scroll-fade-in">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-serif font-bold text-white mb-4 animate-fade-in-up animation-delay-1000">Upcoming Events</h2>
                <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-4 animate-scale-in animation-delay-1200"></div>
                <p className="text-white/80 max-w-2xl mx-auto animate-fade-in-up animation-delay-1400">
                  Mark your calendars for these extraordinary royal celebrations and cultural gatherings.
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {upcomingEvents.map((event, index) => (
                  <div
                    key={index}
                    className={`group bg-white/10 backdrop-blur-lg rounded-3xl overflow-hidden border border-white/20 shadow-2xl hover:shadow-3xl hover:scale-105 hover:-translate-y-2 transition-all duration-300 scroll-fade-in animate-scale-in animation-delay-${1600 + index * 200}`}
                  >
                    {/* Event Header Image */}
                    <div className="h-56 bg-gradient-to-br from-royalGold/30 to-royalBlue/30 relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <i className="fas fa-crown text-6xl text-royalGold/80"></i>
                      </div>
                      <div className="absolute top-4 left-4">
                        <span className="px-3 py-1 bg-royalGold/90 text-royalBlue text-xs font-bold rounded-full">
                          {event.category}
                        </span>
                      </div>
                      {event.ticketsAvailable && (
                        <div className="absolute top-4 right-4">
                          <span className="px-3 py-1 bg-green-500/90 text-white text-xs font-bold rounded-full">
                            Tickets Available
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="p-8">
                      <h3 className="text-2xl font-bold text-white mb-3">{event.title}</h3>
                      <p className="text-white/90 text-sm mb-6 leading-relaxed">{event.description}</p>

                      <div className="space-y-3 mb-6">
                        <div className="flex items-center text-white/80">
                          <div className="w-8 h-8 bg-royalGold/20 rounded-lg flex items-center justify-center mr-3">
                            <i className="fas fa-calendar text-royalGold text-sm"></i>
                          </div>
                          <div>
                            <p className="text-sm font-semibold">{event.date}</p>
                            <p className="text-xs text-white/70">{event.time}</p>
                          </div>
                        </div>
                        <div className="flex items-center text-white/80">
                          <div className="w-8 h-8 bg-royalGold/20 rounded-lg flex items-center justify-center mr-3">
                            <i className="fas fa-map-marker-alt text-royalGold text-sm"></i>
                          </div>
                          <p className="text-sm">{event.location}</p>
                        </div>
                      </div>

                      <div className="flex gap-3">
                        <a
                          href="/rsvp"
                          className="flex-1 px-4 py-3 bg-white/10 border border-white/30 text-white text-sm font-semibold rounded-xl hover:bg-white/20 transition-all duration-300 text-center"
                        >
                          Learn More
                        </a>
                        {event.ticketsAvailable && (
                          <a
                            href="/tickets"
                            className="px-6 py-3 relative overflow-hidden text-royalBlue font-bold text-sm rounded-xl shadow-lg border border-yellow-300 group"
                            style={{
                              background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
                              boxShadow: '0 4px 16px rgba(255, 215, 0, 0.3)'
                            }}
                          >
                            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
                            <span className="relative z-10">Get Tickets</span>
                          </a>
                        )}
                      </div>
                    </div>

                    {/* Hover effect overlay */}
                    <div className="absolute inset-0 bg-gradient-to-br from-royalGold/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-3xl"></div>
                  </div>
                ))}
              </div>
            </div>

            {/* Past Events */}
            <div className="mb-20 scroll-fade-in">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-serif font-bold text-white mb-4 animate-fade-in-up">Past Events</h2>
                <div className="w-24 h-1 bg-gradient-to-r from-gray-400 to-gray-500 mx-auto mb-4 animate-scale-in animation-delay-200"></div>
                <p className="text-white/70 max-w-2xl mx-auto animate-fade-in-up animation-delay-400">
                  Celebrating the successful events that have shaped our Kingdom's journey.
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {pastEvents.map((event, index) => (
                  <div
                    key={index}
                    className={`group bg-white/5 backdrop-blur-lg rounded-3xl overflow-hidden border border-white/10 shadow-xl hover:shadow-2xl hover:scale-105 transition-all duration-300 opacity-90 scroll-fade-in animate-slide-in-bottom animation-delay-${600 + index * 200}`}
                  >
                    {/* Past Event Header */}
                    <div className="h-48 bg-gradient-to-br from-gray-500/20 to-gray-700/20 relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <i className="fas fa-history text-5xl text-gray-400"></i>
                      </div>
                      <div className="absolute top-4 left-4">
                        <span className="px-3 py-1 bg-gray-500/80 text-white text-xs font-bold rounded-full">
                          {event.category}
                        </span>
                      </div>
                      <div className="absolute top-4 right-4">
                        <span className="px-3 py-1 bg-gray-600/80 text-white text-xs font-bold rounded-full">
                          Completed
                        </span>
                      </div>
                    </div>

                    <div className="p-6">
                      <h3 className="text-xl font-bold text-white/90 mb-3">{event.title}</h3>
                      <p className="text-white/70 text-sm mb-4 leading-relaxed">{event.description}</p>

                      <div className="flex items-center text-white/60">
                        <div className="w-8 h-8 bg-gray-500/20 rounded-lg flex items-center justify-center mr-3">
                          <i className="fas fa-calendar text-gray-400 text-sm"></i>
                        </div>
                        <div>
                          <p className="text-sm">{event.date}</p>
                          <p className="text-xs text-white/50">{event.location}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Call to Action */}
            <div className="text-center scroll-fade-in">
              <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl max-w-2xl mx-auto animate-zoom-in hover:scale-105 transition-all duration-500">
                <h3 className="text-2xl font-bold text-white mb-4 animate-fade-in animation-delay-200">Stay Updated</h3>
                <p className="text-white/90 mb-6 leading-relaxed animate-fade-in-up animation-delay-400">
                  Don't miss out on upcoming royal events and cultural celebrations. Subscribe to our newsletter for exclusive updates and early access to tickets.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="/rsvp"
                    className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue font-bold rounded-xl shadow-lg hover:shadow-xl hover:scale-110 hover:-translate-y-1 transition-all duration-300 animate-bounce-in animation-delay-600"
                  >
                    <i className="fas fa-calendar-check mr-2"></i>
                    RSVP for Events
                  </a>
                  <a
                    href="#contact"
                    className="inline-flex items-center px-8 py-4 bg-white/20 backdrop-blur-md border border-white/30 text-white font-bold rounded-xl shadow-lg hover:shadow-xl hover:scale-110 hover:-translate-y-1 transition-all duration-300 animate-bounce-in animation-delay-800"
                  >
                    <i className="fas fa-envelope mr-2"></i>
                    Contact Us
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>
    </main>
  )
}

export default Events
