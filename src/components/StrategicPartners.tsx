'use client'

const StrategicPartners: React.FC = () => {
  const partners = [
    {
      name: 'Remit Global',
      description: 'Strategic financial partner supporting the Kingdom\'s economic initiatives.',
      logo: 'fas fa-globe'
    },
    {
      name: 'Royal Lion',
      description: 'Heritage and cultural partner preserving the Kingdom\'s traditions.',
      logo: 'fas fa-crown'
    },
    {
      name: 'TEF',
      description: 'Educational development partner advancing knowledge and skills in the community.',
      logo: 'fas fa-graduation-cap'
    },
    {
      name: 'Lightace Global',
      description: 'Innovation and technology partner bringing digital solutions to the Kingdom.',
      logo: 'fas fa-lightbulb'
    },
    {
      name: 'Akuapem Nifaman Council',
      description: 'Traditional governance partner supporting the Kingdom\'s leadership structure.',
      logo: 'fas fa-shield-alt'
    }
  ]

  return (
    <section id="strategic-partners" className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/20 via-royalGold/5 to-royalGold/15"></div>
      <div className="absolute inset-0 bg-gradient-to-bl from-transparent via-royalGold/3 to-royalGold/8"></div>

      <div className="container mx-auto px-4 relative z-10">
        <div
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-4">
            Strategic Partners of the Crown
          </h2>
          <p className="text-lg text-white/90 max-w-3xl mx-auto leading-relaxed">
            At the heart of the Kingdom's vision for prosperity, unity and global impact are our Strategic Partners of the Crown.
          </p>
        </div>

        {/* Partners Grid */}
        <div
          className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-8 mb-16"
        >
          {partners.map((partner, index) => (
            <div
              key={index}
              className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 text-center border border-white/20 shadow-xl hover:bg-white/15 transition-all duration-300"
            >
              {/* Logo Circle */}
              <div className="w-20 h-20 mx-auto mb-4 bg-white rounded-full flex items-center justify-center border-4 border-royalGold">
                <i className={`${partner.logo} text-royalBlue text-2xl`}></i>
              </div>

              {/* Partner Name */}
              <h3 className="text-lg font-bold text-white mb-2">{partner.name}</h3>

              {/* Description */}
              <p className="text-white/80 text-sm mb-4 leading-relaxed">{partner.description}</p>

              {/* Visit Website Link */}
              <a
                href="#"
                className="inline-flex items-center text-royalGold hover:text-yellow-400 text-sm font-semibold transition-colors duration-300"
              >
                Visit Website →
              </a>
            </div>
          ))}
        </div>

        {/* View All Partners Button */}
        <div
          className="text-center"
        >
          <a
            href="/partners"
            className="inline-flex items-center px-8 py-4 relative overflow-hidden text-royalBlue font-bold rounded-xl shadow-2xl border-2 border-yellow-300 group"
            style={{
              background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
              boxShadow: '0 12px 40px rgba(255, 215, 0, 0.5), inset 0 3px 6px rgba(255, 255, 255, 0.4), inset 0 -3px 6px rgba(0, 0, 0, 0.3)'
            }}
          >
            {/* Shining animation overlay */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1200 ease-out"></div>
            <span className="relative z-10 font-extrabold">
              View All Partners →
            </span>
          </a>
        </div>
      </div>
    </section>
  )
}

export default StrategicPartners
