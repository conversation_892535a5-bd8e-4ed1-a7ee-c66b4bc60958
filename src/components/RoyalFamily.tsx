'use client'

const RoyalFamily: React.FC = () => {
  const familyMembers = [
    {
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON>a Otutu Ababio V',
      title: 'Akuapem Nfahene/Adukromhene',
      image: '/Website Images/OsuodumgyaOtutuObabioVheadshot.png'
    },
    {
      name: '<PERSON> <PERSON>',
      title: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      image: '/Website Images/KingEllisonHeadshot.png'
    },
    {
      name: '<PERSON>woabrenpong Awo Abena Konamah',
      title: 'Akuapem Nfahemaa/Adukromhemaa',
      image: '/Website Images/OwoabrenpongAwoAbenaKonamahheadshot.png'
    }
  ]

  return (
    <section id="royal-family" className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/5 via-transparent to-royalBlue/5"></div>
      
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 right-20 w-80 h-80 bg-gradient-to-br from-royalGold/8 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tl from-royalBlue/8 to-transparent rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16 scroll-fade-in">
          <h2 className="text-4xl md:text-5xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4 animate-fade-in-up">
            The Royal Family
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6 animate-scale-in animation-delay-400"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed animate-fade-in-up animation-delay-600">
            The Royal Family of Adukrom stands as a sacred pillar of tradition, dignity, and progressive leadership—honoring ancestral wisdom while guiding the Kingdom into a new era of prosperity and global significance.
          </p>
        </div>

        {/* Royal Family Members */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20 max-w-6xl mx-auto">
          {familyMembers.map((member, index) => (
            <div
              key={index}
              className={`group relative h-full scroll-fade-in animate-scale-in animation-delay-${800 + index * 200}`}
            >
              <div className="bg-white/60 backdrop-blur-lg rounded-3xl p-8 border border-white/50 shadow-xl hover:shadow-2xl hover:scale-105 hover:-translate-y-2 transition-all duration-300 h-full flex flex-col justify-between min-h-[400px]">
                {/* Profile Image */}
                <div className="w-40 h-40 mx-auto mb-6 rounded-full overflow-hidden shadow-lg border-4 border-white/50 flex-shrink-0">
                  <img
                    src={member.image}
                    alt={member.name}
                    className="w-full h-full object-cover hover:scale-110 transition-transform duration-500"
                  />
                </div>

                {/* Member Info */}
                <div className="text-center flex-grow flex flex-col justify-center">
                  <h3 className="text-2xl font-bold text-royalBlue mb-2 leading-tight">{member.name}</h3>
                  <p className="text-royalGold font-semibold text-lg">{member.title}</p>
                </div>

                {/* Hover effect overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-royalGold/10 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Family Values */}
        <div className="text-center scroll-fade-in">
          <div className="bg-gradient-to-r from-royalBlue/10 to-royalGold/10 backdrop-blur-lg rounded-3xl p-8 border border-white/30 shadow-2xl max-w-4xl mx-auto hover:shadow-3xl transition-all duration-500">
            <h3 className="text-2xl font-bold text-royalBlue mb-6">Royal Family Values</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center hover:scale-105 transition-transform duration-300">
                <div className="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-royalGold to-yellow-500 rounded-xl flex items-center justify-center">
                  <i className="fas fa-heart text-white"></i>
                </div>
                <h4 className="font-semibold text-royalBlue mb-2">Service</h4>
                <p className="text-gray-700 text-sm">Dedicated to serving the people of Ghana with humility and compassion</p>
              </div>
              <div className="text-center hover:scale-105 transition-transform duration-300">
                <div className="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-royalBlue to-blue-600 rounded-xl flex items-center justify-center">
                  <i className="fas fa-balance-scale text-white"></i>
                </div>
                <h4 className="font-semibold text-royalBlue mb-2">Integrity</h4>
                <p className="text-gray-700 text-sm">Upholding the highest standards of honesty and moral principles</p>
              </div>
              <div className="text-center hover:scale-105 transition-transform duration-300">
                <div className="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-forestGreen to-green-600 rounded-xl flex items-center justify-center">
                  <i className="fas fa-seedling text-white"></i>
                </div>
                <h4 className="font-semibold text-royalBlue mb-2">Legacy</h4>
                <p className="text-gray-700 text-sm">Building a lasting legacy for future generations of Ghanaians</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default RoyalFamily
