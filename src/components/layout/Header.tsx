'use client'

import { useState } from 'react'
import Link from 'next/link'

// TypeScript interfaces
interface DropdownItem {
  name: string
  href: string
}

interface NavigationItem {
  name: string
  href?: string
  dropdown?: DropdownItem[]
}

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false)
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)

  const navigationItems: NavigationItem[] = [
    { name: 'Home', href: '/adukrom' },
    { name: 'About', href: '/adukrom#about-allen' },
    { name: 'Royal Family', href: '/adukrom#royal-family' },
    { name: 'Coronation', href: '/adukrom#royal-coronation' },
    {
      name: 'More',
      dropdown: [
        { name: 'Initiatives', href: '/adukrom#initiatives' },
        { name: 'Strategic Partners', href: '/partners' },
        { name: 'RSVP', href: '/rsvp' },
        { name: 'Events', href: '/events' },
        { name: 'News', href: '/news' },
        { name: 'Streaming', href: '/streaming' }
      ]
    },
    { name: 'Contact', href: '/adukrom#contact' }
  ]

  return (
    <header
      className="fixed top-0 left-0 right-0 bg-royalBlue text-white shadow-lg w-full z-[1000] backdrop-blur-sm overflow-visible"
    >
      {/* Golden gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-royalGold/10 via-transparent to-royalGold/5 pointer-events-none"></div>
      <div className="container mx-auto px-4 py-3 relative z-10">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <div 
            className="flex items-center"
          >
            <Link href="/" className="flex items-center">
              <img
                src="/Website Images/logo.png"
                alt="Adukrom Kingdom Logo"
                className="h-10 w-auto mr-3"
              />
              <div>
                <h1 className="text-xl font-serif font-bold">Adukrom Kingdom</h1>
                <p className="text-xs text-royalGold">The Crown of Africa: Rise of a New Era</p>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex space-x-6 items-center">
            {navigationItems.map((item: NavigationItem) => (
              <div key={item.name} className="relative z-[1001]">
                {item.dropdown ? (
                  <div
                    className="relative group"
                    onMouseEnter={() => setActiveDropdown(item.name)}
                    onMouseLeave={() => {
                      setTimeout(() => {
                        setActiveDropdown(null)
                      }, 300)
                    }}
                  >
                    <button className="relative text-sm font-medium hover:text-royalGold transition-colors duration-300 py-2 px-1 group flex items-center">
                      {item.name}
                      <i className="fas fa-chevron-down ml-1 text-xs transition-transform duration-300 group-hover:rotate-180"></i>
                      <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-royalGold transition-all duration-300 group-hover:w-full"></span>
                    </button>
                    
                    {/* Dropdown Menu */}
                    <div className={`absolute top-full left-0 mt-2 w-56 bg-white backdrop-blur-lg rounded-xl shadow-2xl border border-royalGold/20 overflow-hidden transition-all duration-300 z-[9999] ${
                      activeDropdown === item.name ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'
                    }`}
                    style={{
                      boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(212, 175, 55, 0.1)'
                    }}
                    onMouseEnter={() => setActiveDropdown(item.name)}
                    onMouseLeave={() => {
                      setTimeout(() => {
                        setActiveDropdown(null)
                      }, 300)
                    }}
                    >
                      {item.dropdown.map((dropdownItem: DropdownItem) => (
                        <Link
                          key={dropdownItem.name}
                          href={dropdownItem.href}
                          className="block px-4 py-3 text-sm font-medium text-royalBlue hover:bg-royalGold/10 hover:text-royalGold transition-all duration-300 border-b border-gray-100 last:border-b-0"
                        >
                          {dropdownItem.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                ) : (
                  <Link
                    href={item.href!}
                    className="relative text-sm font-medium hover:text-royalGold transition-colors duration-300 py-2 px-1 group"
                  >
                    {item.name}
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-royalGold transition-all duration-300 group-hover:w-full"></span>
                  </Link>
                )}
              </div>
            ))}

            {/* Tickets Button */}
            <div>
              <Link
                href="/tickets"
                className="ml-4 px-6 py-2 relative overflow-hidden bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 text-royalBlue font-bold rounded-lg shadow-2xl border-2 border-yellow-300 group inline-block"
                style={{
                  background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
                  boxShadow: '0 8px 32px rgba(255, 215, 0, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.3), inset 0 -2px 4px rgba(0, 0, 0, 0.2)'
                }}
              >
                {/* Shining animation overlay */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
                <span className="relative z-10 font-extrabold text-shadow">Purchase Tickets</span>
              </Link>
            </div>

          </nav>

          {/* Mobile Menu Button */}
          <div className="lg:hidden">
            <button
              className="text-white focus:outline-none"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              type="button"
              aria-label="Toggle mobile menu"
            >
              <i className={`fas ${isMenuOpen ? 'fa-times' : 'fa-bars'} text-xl`}></i>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="lg:hidden mt-4 border-t border-royalGold/30 pt-4">
            <div className="flex flex-col space-y-3">
              {navigationItems.map((item: NavigationItem) => (
                <div key={item.name}>
                  {item.dropdown ? (
                    <div>
                      <button
                        className="text-sm font-medium hover:text-royalGold transition-colors duration-300 py-2 flex items-center justify-between w-full"
                        onClick={() => setActiveDropdown(activeDropdown === item.name ? null : item.name)}
                        type="button"
                        aria-expanded={activeDropdown === item.name}
                      >
                        {item.name}
                        <i className={`fas fa-chevron-down text-xs transition-transform duration-300 ${
                          activeDropdown === item.name ? 'rotate-180' : ''
                        }`}></i>
                      </button>
                      
                      {/* Mobile Dropdown Items */}
                      <div className={`ml-4 mt-2 space-y-2 transition-all duration-300 ${
                        activeDropdown === item.name ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0 overflow-hidden'
                      }`}>
                        {item.dropdown.map((dropdownItem: DropdownItem) => (
                          <Link
                            key={dropdownItem.name}
                            href={dropdownItem.href}
                            className="block text-sm text-white/80 hover:text-royalGold transition-colors duration-300 py-1"
                            onClick={() => setIsMenuOpen(false)}
                          >
                            {dropdownItem.name}
                          </Link>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <Link
                      href={item.href!}
                      className="text-sm font-medium hover:text-royalGold transition-colors duration-300 py-2 block"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {item.name}
                    </Link>
                  )}
                </div>
              ))}

              {/* Mobile Tickets Button */}
              <div>
                <Link
                  href="/tickets"
                  className="mt-4 px-6 py-3 relative overflow-hidden text-center rounded-lg shadow-2xl border-2 border-yellow-300 group text-royalBlue font-bold inline-block w-full"
                  onClick={() => setIsMenuOpen(false)}
                  style={{
                    background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
                    boxShadow: '0 8px 32px rgba(255, 215, 0, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.3), inset 0 -2px 4px rgba(0, 0, 0, 0.2)'
                  }}
                >
                  {/* Shining animation overlay */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
                  <span className="relative z-10 font-extrabold text-shadow">Tickets</span>
                </Link>
              </div>

            </div>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
