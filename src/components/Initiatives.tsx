'use client'

const Initiatives: React.FC = () => {
  const initiatives = [
    {
      title: 'Uplift Ghana',
      description: 'A Royal Initiative for National Prosperity, Inclusion, and Innovation, uniting public and private sectors to build a thriving, future-ready Ghana.',
      status: 'Active',
      progress: 75,
      gradient: 'from-royalGold to-yellow-500',
      icon: 'fas fa-flag',
      image: '/Website Images/Flags Ghana.png',
      link: '/uplift-ghana',
      features: ['Economic Development', 'Job Creation', 'Innovation Hubs', 'Skills Training']
    },
    {
      title: 'Education Fund',
      description: 'Supporting educational opportunities for youth across the Eastern Region through scholarships and school infrastructure development.',
      status: 'Expanding',
      progress: 60,
      gradient: 'from-royalBlue to-blue-600',
      icon: 'fas fa-graduation-cap',
      image: '/Website Images/Education.jpg',
      link: '#',
      features: ['Scholarships', 'School Infrastructure', 'Teacher Training', 'Digital Learning']
    },
    {
      title: 'Healthcare Access',
      description: 'Improving healthcare facilities and services in rural communities through mobile clinics and medical training programs.',
      status: 'Planning',
      progress: 40,
      gradient: 'from-forestGreen to-green-600',
      icon: 'fas fa-heartbeat',
      image: '/Website Images/Medical.jpg',
      link: '#',
      features: ['Mobile Clinics', 'Medical Training', 'Health Education', 'Emergency Response']
    },
    {
      title: 'Cultural Preservation',
      description: 'Documenting and preserving traditional knowledge, arts, and practices through digital archives and community festivals.',
      status: 'Ongoing',
      progress: 85,
      gradient: 'from-purple-600 to-pink-600',
      icon: 'fas fa-palette',
      image: '/Website Images/kente-1.png',
      link: '#',
      features: ['Digital Archives', 'Cultural Festivals', 'Artisan Support', 'Heritage Sites']
    },
    {
      title: 'Sustainable Development',
      description: 'Promoting eco-friendly practices and renewable energy solutions to protect our natural resources for future generations.',
      status: 'Research',
      progress: 30,
      gradient: 'from-teal-600 to-cyan-600',
      icon: 'fas fa-leaf',
      image: '/Website Images/shu-Ghana-Umbrella-Rock_243946015.png',
      link: '#',
      features: ['Renewable Energy', 'Waste Management', 'Conservation', 'Green Technology']
    }
  ]

  return (
    <section id="initiatives" className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/5 via-transparent to-royalBlue/5"></div>
      
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-10 right-10 w-96 h-96 bg-gradient-to-br from-royalGold/8 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 left-10 w-80 h-80 bg-gradient-to-tl from-royalBlue/8 to-transparent rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4">
            Royal Initiatives
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Transforming Ghana through strategic initiatives that empower communities, preserve culture, and build a sustainable future for all.
          </p>
        </div>

        {/* Initiatives Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-16">
          {initiatives.map((initiative, index) => (
            <div
              key={index}
              className="group relative"
            >
              <div className="bg-white/80 backdrop-blur-lg rounded-2xl overflow-hidden border border-white/60 shadow-lg hover:shadow-xl transition-all duration-300 h-full">
                {/* Initiative Image */}
                <div className="h-32 relative overflow-hidden">
                  <img
                    src={initiative.image}
                    alt={initiative.title}
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                </div>

                {/* Content */}
                <div className="p-4">
                  <div className="flex items-center justify-center mb-3">
                    <div className="w-12 h-12 bg-royalGold/20 rounded-full flex items-center justify-center">
                      <i className={`${initiative.icon} text-royalGold text-lg`}></i>
                    </div>
                  </div>

                  <h3 className="text-lg font-bold text-royalBlue text-center mb-3">{initiative.title}</h3>

                  <p className="text-gray-700 text-sm leading-relaxed text-center mb-4">
                    {initiative.description}
                  </p>

                  {/* Action Link */}
                  <div className="text-center">
                    <a
                      href={initiative.link}
                      className="inline-block text-royalGold font-semibold text-sm hover:text-royalBlue transition-colors duration-300"
                    >
                      {initiative.link === '#' ? 'More Info Coming Soon' : 'Learn More →'}
                    </a>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center">
          <p className="text-lg text-royalBlue font-medium mb-6">
            Interested in partnering with us on these initiatives?
          </p>

          {/* Golden Ticket Button */}
          <a
            href="#contact"
            className="inline-flex items-center px-8 py-4 relative overflow-hidden bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 text-royalBlue font-bold rounded-xl shadow-2xl border-2 border-yellow-300 group"
            style={{
              background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
              boxShadow: '0 8px 32px rgba(255, 215, 0, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.3), inset 0 -2px 4px rgba(0, 0, 0, 0.2)'
            }}
          >
            {/* Shining animation overlay */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>

            <span className="relative z-10 flex items-center font-extrabold text-shadow">
              <i className="fas fa-handshake mr-3 text-lg"></i>
              Partner With Us
              <i className="fas fa-arrow-right ml-3 text-sm"></i>
            </span>
          </a>
        </div>


      </div>
    </section>
  )
}

export default Initiatives
