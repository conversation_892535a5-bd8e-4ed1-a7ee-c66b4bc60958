'use client'

const AboutAllen: React.FC = () => {
  return (
    <section id="about-allen" className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/5 via-transparent to-royalBlue/5"></div>
      
      {/* Floating background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 right-20 w-80 h-80 bg-gradient-to-br from-royalGold/8 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-tl from-royalBlue/8 to-transparent rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16 scroll-fade-in">
          <h2 className="text-4xl md:text-5xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4 animate-fade-in-up">
            About His Royal Majesty
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6 animate-scale-in animation-delay-400"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed animate-fade-in-up animation-delay-600">
            His Royal Majesty King Allen Ellison, Mpuntuhene of Adukrom - A visionary leader dedicated to preserving Ghana's rich heritage while building a prosperous future for all.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-16 items-start">
          {/* Content - Left Side (2/3 width) */}
          <div className="lg:col-span-2 space-y-8">
            {/* Biography */}
            <div className="bg-white/50 backdrop-blur-lg rounded-xl p-5 border border-white/30 shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 scroll-slide-left animate-slide-in-left animation-delay-800">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 bg-gradient-to-br from-royalGold to-yellow-500 rounded-lg flex items-center justify-center mr-3 animate-rotate-in animation-delay-1000">
                  <i className="fas fa-user text-white text-sm"></i>
                </div>
                <h3 className="text-xl font-bold text-royalBlue">Biography</h3>
              </div>

              <div className="space-y-3">
                <p className="text-gray-700 leading-relaxed">
                  His Royal Majesty was born into the noble lineage of the Ashanti Dynasty, one of Ghana's most revered royal houses. From an early age, he was groomed in the traditions and customs of royal leadership, receiving both traditional education and modern academic training.
                </p>
                <p className="text-gray-700 leading-relaxed">
                  After completing his education at prestigious institutions both in Ghana and abroad, His Majesty dedicated himself to public service and cultural preservation. His work in sustainable development and cultural heritage has earned him numerous accolades and the respect of leaders worldwide.
                </p>
                <div className="mt-4">
                  <a
                    href="/biography"
                    className="inline-flex items-center text-royalBlue hover:text-royalGold transition-colors duration-300 font-semibold"
                  >
                    Read More
                    <i className="fas fa-arrow-right ml-2 text-sm"></i>
                  </a>
                </div>
              </div>
            </div>

            {/* Vision */}
            <div className="bg-white/50 backdrop-blur-lg rounded-xl p-5 border border-white/30 shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 scroll-slide-left animate-slide-in-left animation-delay-1200">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 bg-gradient-to-br from-royalGold to-yellow-500 rounded-lg flex items-center justify-center mr-3 animate-rotate-in animation-delay-1400">
                  <i className="fas fa-eye text-white text-sm"></i>
                </div>
                <h3 className="text-lg font-bold text-royalBlue">Vision</h3>
              </div>
              <p className="text-gray-700 leading-relaxed">
                To establish Ghana as a beacon of cultural preservation and sustainable development in Africa, where traditional wisdom and modern innovation harmoniously coexist to create prosperity for all citizens.
              </p>
            </div>

            {/* Mission */}
            <div className="bg-white/50 backdrop-blur-lg rounded-xl p-5 border border-white/30 shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 scroll-slide-left animate-slide-in-left animation-delay-1600">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 bg-gradient-to-br from-royalBlue to-blue-600 rounded-lg flex items-center justify-center mr-3 animate-rotate-in animation-delay-1800">
                  <i className="fas fa-target text-white text-sm"></i>
                </div>
                <h3 className="text-lg font-bold text-royalBlue">Mission</h3>
              </div>
              <p className="text-gray-700 leading-relaxed">
                To lead with wisdom, serve with humility, and govern with justice, ensuring that every citizen has the opportunity to reach their full potential through sustainable development and cultural preservation.
              </p>
            </div>

            {/* Royal Message */}
            <div className="bg-gradient-to-br from-royalBlue/5 to-royalGold/5 backdrop-blur-lg rounded-xl p-6 border border-royalGold/20 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-gradient-to-br from-royalGold to-yellow-500 rounded-lg flex items-center justify-center mr-3">
                  <i className="fas fa-quote-left text-white text-sm"></i>
                </div>
                <h3 className="text-lg font-bold text-royalBlue">Royal Message</h3>
              </div>

              <blockquote className="text-gray-700 leading-relaxed italic mb-4 pl-3 border-l-2 border-royalGold/30">
                "Our kingdom's strength lies in honoring our ancestors while building bridges to the future. Together, we will create prosperity that respects our traditions and embraces innovation for The Rebirth of Adukrom."
              </blockquote>

              <div className="flex items-center justify-end">
                <div className="text-right text-sm">
                  <div className="text-royalGold font-semibold">His Royal Majesty</div>
                  <div className="text-royalBlue">King Allen Ellison</div>
                </div>
                <div className="ml-3 w-8 h-8 bg-gradient-to-br from-royalGold to-yellow-500 rounded-full flex items-center justify-center">
                  <i className="fas fa-crown text-white text-xs"></i>
                </div>
              </div>
            </div>
          </div>

          {/* Profile Image - Right Side (1/3 width) */}
          <div className="relative lg:sticky lg:top-24 scroll-slide-right animate-slide-in-right animation-delay-1000">
            <div className="relative overflow-hidden rounded-2xl shadow-lg group hover:shadow-2xl transition-all duration-500 animate-zoom-in animation-delay-1200">
              <img
                src="/website-images/Allen-Ellison-Profile-scaled-1.png"
                alt="His Royal Majesty King Allen Ellison"
                className="w-full h-full object-cover aspect-[3/4] transition-transform duration-500 group-hover:scale-110 animate-float"
                onError={(e) => {
                  // Fallback if image doesn't exist
                  e.currentTarget.style.display = 'none';
                  const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                  if (nextElement) {
                    nextElement.style.display = 'flex';
                  }
                }}
              />
              {/* Fallback placeholder */}
              <div className="aspect-[3/4] bg-gradient-to-br from-royalBlue/20 to-royalGold/20 flex items-center justify-center" style={{display: 'none'}}>
                <div className="text-4xl text-royalBlue/30">
                  <i className="fas fa-crown"></i>
                </div>
              </div>

              {/* Subtle overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-royalBlue/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              {/* Small royal badge */}
              <div className="absolute top-3 right-3 bg-royalGold/90 backdrop-blur-sm rounded-full p-2 shadow-md">
                <i className="fas fa-crown text-white text-sm"></i>
              </div>
            </div>

            {/* Minimal decorative elements */}
            <div className="absolute -top-1 -right-1 w-8 h-8 bg-gradient-to-br from-royalGold/20 to-yellow-400/20 rounded-full blur-lg"></div>
            <div className="absolute -bottom-1 -left-1 w-10 h-10 bg-gradient-to-tr from-royalBlue/20 to-blue-500/20 rounded-full blur-lg"></div>

            {/* Compact title below image */}
            <div className="mt-4 text-center">
              <h4 className="text-lg font-bold text-royalBlue mb-1">His Royal Majesty</h4>
              <p className="text-royalGold font-semibold text-sm">King Allen Ellison</p>
              <p className="text-gray-600 text-xs mt-1">Mpuntuhene of Adukrom</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default AboutAllen
