'use client'

import Image from 'next/image'
import { getImageUrl, isExternalImageSource } from '../utils/imageConfig'

const RoyalGallery: React.FC = () => {

  // Main gallery images - displayed in the first grid
  const galleryImages = [
    {
      title: 'Royal Palace',
      description: 'The majestic Royal Palace in all its grandeur',
      imagePath: 'gallery/palace.jpg'
    },
    {
      title: 'Royal Coronation',
      description: 'Historic moments from the royal coronation ceremony',
      imagePath: 'gallery/coronation1.jpg'
    },
    {
      title: 'Traditional Ceremony',
      description: 'Sacred traditional ceremonies and rituals',
      imagePath: 'gallery/ceremony.jpg'
    },
    {
      title: 'Community Gathering',
      description: 'Royal engagement with the local community',
      imagePath: 'gallery/community.jpg'
    },
    {
      title: 'Traditional Dance',
      description: 'Cultural dance performances and celebrations',
      imagePath: 'gallery/traditional-dance.jpg'
    },
    {
      title: 'Royal Artifacts',
      description: 'Precious artifacts and royal treasures',
      imagePath: 'gallery/artifacts.jpg'
    }
  ]

  // Featured images - displayed in the second grid (different from main gallery)
  const featuredImages = [
    {
      title: 'Royal Coronation Ceremony',
      description: 'The historic coronation ceremony in all its splendor',
      imagePath: 'gallery/coronation1.jpg'
    },
    {
      title: 'Sacred Ceremonies',
      description: 'Traditional sacred ceremonies and royal rituals',
      imagePath: 'gallery/ceremony.jpg'
    },
    {
      title: 'Community Engagement',
      description: 'Royal engagement with the beloved community',
      imagePath: 'gallery/community.jpg'
    },
    {
      title: 'Cultural Heritage',
      description: 'Celebrating our rich cultural heritage and traditions',
      imagePath: 'gallery/traditional-dance.jpg'
    },
    {
      title: 'Royal Treasures',
      description: 'Ancient artifacts and precious royal treasures',
      imagePath: 'gallery/artifacts.jpg'
    },
    {
      title: 'Royal Palace Gardens',
      description: 'The magnificent palace and its beautiful surroundings',
      imagePath: 'gallery/palace.jpg'
    }
  ]

  return (
    <section id="royal-gallery" className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/5 via-transparent to-royalBlue/5"></div>
      
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-royalGold/8 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-tl from-royalBlue/8 to-transparent rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4">
            Royal Gallery
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Explore the rich cultural heritage and royal traditions of the Kingdom through our curated gallery of images, capturing moments of history, culture, and celebration.
          </p>
        </div>

        {/* Main Gallery Grid */}
        <div className="mb-20">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {galleryImages.map((image, index) => (
              <div
                key={index}
                className="group relative cursor-pointer"
              >
                <div className="rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 bg-gray-100 relative aspect-[16/10] h-64">
                  <Image
                    src={getImageUrl(image.imagePath)}
                    alt={image.title}
                    fill
                    className="object-cover object-center transition-transform duration-300 group-hover:scale-105"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    unoptimized={isExternalImageSource()}
                    priority={index < 3}
                    quality={90}
                  />

                  {/* Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  {/* Content */}
                  <div className="absolute bottom-0 left-0 right-0 p-4 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                    <h4 className="font-bold text-lg mb-1 drop-shadow-lg">{image.title}</h4>
                    <p className="text-sm text-white/90 drop-shadow-md">{image.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Featured Images Grid */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-serif font-bold text-royalBlue mb-4">Featured Images</h3>
            <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-4"></div>
            <p className="text-gray-700 max-w-2xl mx-auto">
              A selection of our most captivating images showcasing the beauty and grandeur of the Royal Kingdom.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredImages.map((image, index) => (
              <div
                key={index}
                className="group relative cursor-pointer"
              >
                <div className="rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 bg-gray-100 relative aspect-[16/10] h-64">
                  <Image
                    src={getImageUrl(image.imagePath)}
                    alt={image.title}
                    fill
                    className="object-cover object-center transition-transform duration-300 group-hover:scale-105"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    unoptimized={isExternalImageSource()}
                    priority={index < 3}
                    quality={90}
                  />

                  {/* Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  {/* Content */}
                  <div className="absolute bottom-0 left-0 right-0 p-4 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                    <h4 className="font-bold text-lg mb-1 drop-shadow-lg">{image.title}</h4>
                    <p className="text-sm text-white/90 drop-shadow-md">{image.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-royalBlue/10 to-royalGold/10 backdrop-blur-lg rounded-3xl p-8 border border-white/30 shadow-2xl max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-royalBlue mb-4">Explore More</h3>
            <p className="text-gray-700 mb-6 leading-relaxed">
              Discover the complete collection of royal images, historical documents, and cultural artifacts in our comprehensive digital archive.
            </p>
            <a
              href="/gallery"
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <i className="fas fa-images mr-2"></i>
              View Full Gallery
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default RoyalGallery
