'use client'

const RoyalGallery: React.FC = () => {
  const featuredImages = [
    {
      title: 'Royal Palace at Sunset',
      description: 'The majestic Royal Palace illuminated by the golden hour',
      size: 'large',
      url: 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop&crop=center'
    },
    {
      title: 'Traditional Kente Weaving',
      description: 'Master craftsmen creating the iconic Kente cloth',
      size: 'medium',
      url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=400&fit=crop&crop=center'
    },
    {
      title: 'Royal Guard Ceremony',
      description: 'The changing of the Royal Guard in traditional regalia',
      size: 'medium',
      url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=400&fit=crop&crop=center'
    },
    {
      title: 'Cultural Dance Performance',
      description: 'Traditional dancers performing at the royal court',
      size: 'small',
      url: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400&h=300&fit=crop&crop=center'
    },
    {
      title: 'Royal Banquet Hall',
      description: 'The grand banquet hall prepared for state dinners',
      size: 'small',
      url: 'https://images.unsplash.com/photo-1519167758481-83f29c8c2434?w=400&h=300&fit=crop&crop=center'
    },
    {
      title: 'Crown Jewels Display',
      description: 'The magnificent crown jewels of the Kingdom',
      size: 'small',
      url: 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=400&h=300&fit=crop&crop=center'
    }
  ]

  return (
    <section id="royal-gallery" className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-cream via-ivory to-cream"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/5 via-transparent to-royalBlue/5"></div>
      
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 right-20 w-96 h-96 bg-gradient-to-br from-royalGold/8 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-tl from-royalBlue/8 to-transparent rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-4">
            Royal Gallery
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto leading-relaxed">
            Explore the rich cultural heritage and royal traditions of the Kingdom through our curated gallery of images, capturing moments of history, culture, and celebration.
          </p>
        </div>

        {/* Featured Images Grid */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-serif font-bold text-royalBlue mb-4">Featured Images</h3>
            <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-4"></div>
            <p className="text-gray-700 max-w-2xl mx-auto">
              A selection of our most captivating images showcasing the beauty and grandeur of the Royal Kingdom.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {featuredImages.map((image, index) => (
              <div
                key={index}
                className={`group relative cursor-pointer ${
                  image.size === 'large' ? 'md:col-span-2 md:row-span-2' :
                  image.size === 'medium' ? 'md:col-span-2' : 'md:col-span-1'
                }`}
              >
                <div className={`bg-gradient-to-br from-gray-200 to-gray-300 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 ${
                  image.size === 'large' ? 'h-80' :
                  image.size === 'medium' ? 'h-40' : 'h-32'
                }`}>
                  {/* Image Placeholder */}
                  <div className="w-full h-full bg-gradient-to-br from-royalBlue/20 to-royalGold/20 relative overflow-hidden">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <i className="fas fa-image text-gray-400 text-4xl"></i>
                    </div>
                    
                    {/* Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    
                    {/* Content */}
                    <div className="absolute bottom-0 left-0 right-0 p-4 text-white transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                      <h4 className="font-bold text-sm mb-1">{image.title}</h4>
                      <p className="text-xs text-white/90">{image.description}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-royalBlue/10 to-royalGold/10 backdrop-blur-lg rounded-3xl p-8 border border-white/30 shadow-2xl max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-royalBlue mb-4">Explore More</h3>
            <p className="text-gray-700 mb-6 leading-relaxed">
              Discover the complete collection of royal images, historical documents, and cultural artifacts in our comprehensive digital archive.
            </p>
            <a
              href="/gallery"
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <i className="fas fa-images mr-2"></i>
              View Full Gallery
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default RoyalGallery
