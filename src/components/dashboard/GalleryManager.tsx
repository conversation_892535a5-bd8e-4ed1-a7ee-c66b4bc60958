'use client'

import { useState, useEffect } from 'react'
import ImageUpload from './ImageUpload'
import ImageGrid from './ImageGrid'

interface GalleryImage {
  id: string
  title: string
  description: string
  imagePath: string
  imageUrl?: string
  category: string
  uploadedAt: string
  featured: boolean
  tags?: string[]
}

const GalleryManager: React.FC = () => {
  const [images, setImages] = useState<GalleryImage[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [showUpload, setShowUpload] = useState(false)

  // Mock data - will be replaced with Firebase
  const mockImages: GalleryImage[] = [
    {
      id: '1',
      title: 'Royal Palace',
      description: 'The majestic Royal Palace in all its grandeur',
      imagePath: 'gallery/palace.jpg',
      category: 'palace',
      uploadedAt: '2024-01-15T10:00:00Z',
      featured: true,
      tags: ['palace', 'architecture', 'royal']
    },
    {
      id: '2',
      title: 'Royal Coronation Ceremony',
      description: 'Historic moments from the royal coronation ceremony',
      imagePath: 'gallery/coronation1.jpg',
      category: 'royal-events',
      uploadedAt: '2024-01-14T15:30:00Z',
      featured: true,
      tags: ['coronation', 'ceremony', 'royal']
    },
    {
      id: '3',
      title: 'Traditional Ceremony',
      description: 'Sacred traditional ceremonies and rituals',
      imagePath: 'gallery/ceremony.jpg',
      category: 'ceremonies',
      uploadedAt: '2024-01-13T09:15:00Z',
      featured: false,
      tags: ['traditional', 'ceremony', 'culture']
    },
    {
      id: '4',
      title: 'Community Gathering',
      description: 'Royal engagement with the local community',
      imagePath: 'gallery/community.jpg',
      category: 'community',
      uploadedAt: '2024-01-12T14:20:00Z',
      featured: false,
      tags: ['community', 'engagement', 'people']
    }
  ]

  const categories = [
    { id: 'all', name: 'All Images' },
    { id: 'royal-events', name: 'Royal Events' },
    { id: 'ceremonies', name: 'Ceremonies' },
    { id: 'community', name: 'Community' },
    { id: 'culture', name: 'Culture & Heritage' },
    { id: 'palace', name: 'Palace & Architecture' }
  ]

  useEffect(() => {
    // Load images from Firebase
    const loadImages = async () => {
      setLoading(true)
      try {
        // TODO: Replace with actual Firebase call
        setTimeout(() => {
          setImages(mockImages)
          setLoading(false)
        }, 1000)
      } catch (error) {
        console.error('Error loading images:', error)
        setLoading(false)
      }
    }

    loadImages()
  }, [])

  const filteredImages = selectedCategory === 'all' 
    ? images 
    : images.filter(img => img.category === selectedCategory)

  const handleImageUpload = (newImage: Omit<GalleryImage, 'id' | 'uploadedAt'>) => {
    const image: GalleryImage = {
      ...newImage,
      id: Date.now().toString(),
      uploadedAt: new Date().toISOString()
    }
    setImages(prev => [image, ...prev])
    setShowUpload(false)
  }

  const handleImageUpdate = (id: string, updates: Partial<GalleryImage>) => {
    setImages(prev => prev.map(img => 
      img.id === id ? { ...img, ...updates } : img
    ))
  }

  const handleImageDelete = (id: string) => {
    setImages(prev => prev.filter(img => img.id !== id))
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Gallery Management</h2>
          <p className="text-gray-600">Manage your royal gallery images</p>
        </div>
        <button
          onClick={() => setShowUpload(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium flex items-center"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Upload Image
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-2xl font-bold text-blue-600">{images.length}</div>
          <div className="text-sm text-gray-600">Total Images</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-2xl font-bold text-green-600">{images.filter(img => img.featured).length}</div>
          <div className="text-sm text-gray-600">Featured</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-2xl font-bold text-purple-600">{categories.length - 1}</div>
          <div className="text-sm text-gray-600">Categories</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow">
          <div className="text-2xl font-bold text-orange-600">
            {images.filter(img => {
              const uploadDate = new Date(img.uploadedAt)
              const weekAgo = new Date()
              weekAgo.setDate(weekAgo.getDate() - 7)
              return uploadDate > weekAgo
            }).length}
          </div>
          <div className="text-sm text-gray-600">This Week</div>
        </div>
      </div>

      {/* Category Filter */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                selectedCategory === category.id
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category.name}
              {category.id !== 'all' && (
                <span className="ml-1 text-xs">
                  ({images.filter(img => img.category === category.id).length})
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Image Grid */}
      <ImageGrid
        images={filteredImages}
        loading={loading}
        onImageUpdate={handleImageUpdate}
        onImageDelete={handleImageDelete}
      />

      {/* Upload Modal */}
      {showUpload && (
        <ImageUpload
          onUpload={handleImageUpload}
          onClose={() => setShowUpload(false)}
        />
      )}
    </div>
  )
}

export default GalleryManager
