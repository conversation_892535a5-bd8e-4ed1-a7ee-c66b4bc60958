'use client'

const RoyalCoronation: React.FC = () => {

  const events = [
    {
      title: 'Royal Coronation Ceremony',
      date: 'August 29, 2025',
      time: '3:30 AM EST',
      location: 'Adukrom Palace Grounds',
      description: 'The historic coronation ceremony of His Royal Majesty King <PERSON>, marking the beginning of a new era for the Kingdom.',
      gradient: 'from-royalGold to-yellow-500',
      icon: 'fas fa-crown',
      image: '/website-images/Royal Coronation Ceremony.png'
    },
    {
      title: 'Royal Gala Dinner',
      date: 'August 30, 2025',
      time: '5:30 AM EST',
      location: 'Safari Valley Resort, Ghana',
      description: 'An elegant evening celebration featuring traditional Ghanaian cuisine, cultural performances, and royal presentations.',
      gradient: 'from-royalBlue to-blue-600',
      icon: 'fas fa-utensils',
      image: '/website-images/Royal Gala Dinner.png'
    },
    {
      title: 'Global Economic Forum',
      date: 'August 31, 2025',
      time: '05:30 AM EST',
      location: 'Safari Valley Resort, Ghana',
      description: 'A forward-looking conference bringing together investors, entrepreneurs, and leaders to explore economic opportunities and partnerships with the Kingdom.',
      gradient: 'from-royalBlue to-blue-600',
      icon: 'fas fa-handshake',
      image: '/website-images/Global Economic Forum.webp'
    }
  ]

  return (
    <section id="royal-coronation" className="py-20 relative overflow-hidden">
      {/* Background with gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-royalBlue via-royalBlue/95 to-royalBlue/90"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-royalGold/20 via-royalGold/5 to-royalGold/15"></div>
      <div className="absolute inset-0 bg-gradient-to-bl from-transparent via-royalGold/3 to-royalGold/8"></div>
      
      {/* Animated background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="royal-pattern w-full h-full"></div>
      </div>

      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-10 right-10 w-64 h-64 bg-gradient-to-br from-royalGold/20 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 left-10 w-80 h-80 bg-gradient-to-tl from-white/10 to-transparent rounded-full blur-2xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-serif font-bold text-white mb-4">
            Royal Coronation 2025
          </h2>
          <div className="w-32 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
          <p className="text-lg text-white/90 max-w-3xl mx-auto leading-relaxed">
            Join us for the historic coronation ceremony of His Royal Majesty, a once-in-a-generation celebration of Ghana's royal heritage.
          </p>
        </div>



        {/* Events Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          {events.map((event, index) => (
            <div
              key={index}
              className="group relative h-full"
            >
              <div className="bg-white/10 backdrop-blur-lg rounded-3xl overflow-hidden border border-white/20 shadow-2xl hover:shadow-3xl transition-all duration-300 h-full flex flex-col min-h-[650px]">
                {/* Event Image */}
                <div className="w-full h-48 overflow-hidden">
                  <img
                    src={event.image}
                    alt={event.title}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Event Header */}
                <div className={`bg-gradient-to-r ${event.gradient} p-6 relative overflow-hidden`}>
                  <div className="absolute inset-0 bg-black/10"></div>
                  <div className="relative z-10 flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-bold text-white mb-1">{event.title}</h3>
                      <p className="text-white/90 text-sm">{event.date}</p>
                    </div>
                    <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
                      <i className={`${event.icon} text-white text-xl`}></i>
                    </div>
                  </div>
                </div>

                {/* Event Content */}
                <div className="p-6 flex-grow flex flex-col">
                  <div className="mb-4">
                    <div className="flex items-center text-white/80 text-sm mb-2">
                      <i className="fas fa-clock mr-2 text-royalGold"></i>
                      {event.time}
                    </div>
                    <div className="flex items-center text-white/80 text-sm mb-4">
                      <i className="fas fa-map-marker-alt mr-2 text-royalGold"></i>
                      {event.location}
                    </div>
                  </div>

                  <p className="text-white/90 text-sm leading-relaxed mb-6 flex-grow">
                    {event.description}
                  </p>

                  <div className="flex gap-3 mt-auto">
                    <a
                      href="/rsvp"
                      className="flex-1 px-4 py-3 bg-white/10 border border-white/30 text-white text-sm font-semibold rounded-xl hover:bg-white/20 transition-all duration-300 text-center"
                    >
                      RSVP
                    </a>
                    <a
                      href="/tickets"
                      className="px-6 py-3 relative overflow-hidden text-royalBlue font-bold text-sm rounded-xl shadow-lg border border-yellow-300 group"
                      style={{
                        background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 25%, #ffd700 50%, #b8860b 75%, #ffd700 100%)',
                        boxShadow: '0 4px 16px rgba(255, 215, 0, 0.3)'
                      }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></div>
                      <span className="relative z-10">Purchase Ticket</span>
                    </a>
                  </div>
                </div>

                {/* Hover effect overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-royalGold/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-3xl"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Information */}
        <div className="text-center">
          <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 shadow-2xl max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">Need Assistance?</h3>
            <p className="text-white/90 mb-6 leading-relaxed">
              For accommodation and travel information, please contact our Royal Hospitality Team. 
              We're here to ensure your experience is memorable and comfortable.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="#contact"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-royalGold to-yellow-500 text-royalBlue font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <i className="fas fa-phone mr-2"></i>
                Contact Hospitality Team
              </a>
              <a
                href="/rsvp"
                className="inline-flex items-center px-6 py-3 bg-white/20 backdrop-blur-md border border-white/30 text-white font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <i className="fas fa-calendar-check mr-2"></i>
                RSVP Now
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default RoyalCoronation
