// Gallery service for Firebase integration
import {
  collection,
  getDocs,
  doc,
  getDoc,
  query,
  where,
  orderBy,
  limit
} from 'firebase/firestore'
import { ref, getDownloadURL } from 'firebase/storage'
import { db, storage } from '../lib/firebase'

export interface GalleryImage {
  id: string
  title: string
  description: string
  imagePath: string
  imageUrl?: string // Firebase storage URL
  category: string
  uploadedAt: string
  featured: boolean
  tags?: string[]
  metadata?: {
    size: number
    width: number
    height: number
    format: string
  }
}

export interface GalleryCategory {
  id: string
  name: string
  description: string
  icon: string
  imageCount: number
}

// Mock data for development - will be replaced with Firebase calls
const mockImages: GalleryImage[] = [
  {
    id: '1',
    title: 'Royal Palace',
    description: 'The majestic Royal Palace in all its grandeur',
    imagePath: 'gallery/palace.jpg',
    category: 'palace',
    uploadedAt: '2024-01-15T10:00:00Z',
    featured: true,
    tags: ['palace', 'architecture', 'royal'],
    metadata: {
      size: 1024000,
      width: 1920,
      height: 1080,
      format: 'jpg'
    }
  },
  {
    id: '2',
    title: 'Royal Coronation Ceremony',
    description: 'Historic moments from the royal coronation ceremony',
    imagePath: 'gallery/coronation1.jpg',
    category: 'royal-events',
    uploadedAt: '2024-01-14T15:30:00Z',
    featured: true,
    tags: ['coronation', 'ceremony', 'royal', 'historic'],
    metadata: {
      size: 2048000,
      width: 2560,
      height: 1440,
      format: 'jpg'
    }
  },
  {
    id: '3',
    title: 'Traditional Ceremony',
    description: 'Sacred traditional ceremonies and rituals',
    imagePath: 'gallery/ceremony.jpg',
    category: 'ceremonies',
    uploadedAt: '2024-01-13T09:15:00Z',
    featured: false,
    tags: ['traditional', 'ceremony', 'culture'],
    metadata: {
      size: 1536000,
      width: 1920,
      height: 1280,
      format: 'jpg'
    }
  },
  {
    id: '4',
    title: 'Community Gathering',
    description: 'Royal engagement with the local community',
    imagePath: 'gallery/community.jpg',
    category: 'community',
    uploadedAt: '2024-01-12T14:20:00Z',
    featured: false,
    tags: ['community', 'engagement', 'people'],
    metadata: {
      size: 1280000,
      width: 1600,
      height: 1200,
      format: 'jpg'
    }
  },
  {
    id: '5',
    title: 'Traditional Dance',
    description: 'Cultural dance performances and celebrations',
    imagePath: 'gallery/traditional-dance.jpg',
    category: 'culture',
    uploadedAt: '2024-01-11T16:45:00Z',
    featured: false,
    tags: ['dance', 'culture', 'performance', 'traditional'],
    metadata: {
      size: 1792000,
      width: 2048,
      height: 1365,
      format: 'jpg'
    }
  },
  {
    id: '6',
    title: 'Royal Artifacts',
    description: 'Precious artifacts and royal treasures',
    imagePath: 'gallery/artifacts.jpg',
    category: 'culture',
    uploadedAt: '2024-01-10T11:30:00Z',
    featured: false,
    tags: ['artifacts', 'treasures', 'royal', 'heritage'],
    metadata: {
      size: 1024000,
      width: 1440,
      height: 1080,
      format: 'jpg'
    }
  }
]

const mockCategories: GalleryCategory[] = [
  { id: 'all', name: 'All Images', description: 'All gallery images', icon: 'fas fa-images', imageCount: 6 },
  { id: 'royal-events', name: 'Royal Events', description: 'Royal ceremonies and events', icon: 'fas fa-crown', imageCount: 1 },
  { id: 'ceremonies', name: 'Ceremonies', description: 'Traditional and royal ceremonies', icon: 'fas fa-church', imageCount: 1 },
  { id: 'community', name: 'Community', description: 'Community engagement activities', icon: 'fas fa-users', imageCount: 1 },
  { id: 'culture', name: 'Culture & Heritage', description: 'Cultural heritage and traditions', icon: 'fas fa-palette', imageCount: 2 },
  { id: 'palace', name: 'Palace & Architecture', description: 'Palace and architectural images', icon: 'fas fa-building', imageCount: 1 }
]

// Gallery service functions
export class GalleryService {
  
  // Get all images
  static async getAllImages(): Promise<GalleryImage[]> {
    try {
      const q = query(
        collection(db, 'gallery'),
        orderBy('uploadedAt', 'desc')
      )
      const snapshot = await getDocs(q)

      const images = await Promise.all(
        snapshot.docs.map(async (docSnap) => {
          const data = docSnap.data()
          let imageUrl = data.imageUrl

          // If no imageUrl, try to get from storage
          if (!imageUrl && data.imagePath) {
            try {
              const storageRef = ref(storage, data.imagePath)
              imageUrl = await getDownloadURL(storageRef)
            } catch (error) {
              console.warn('Could not get download URL for:', data.imagePath)
            }
          }

          return {
            id: docSnap.id,
            ...data,
            imageUrl
          } as GalleryImage
        })
      )

      return images
    } catch (error) {
      console.error('Error fetching images from Firebase:', error)
      // Fallback to mock data
      return mockImages
    }
  }

  // Get images by category
  static async getImagesByCategory(category: string): Promise<GalleryImage[]> {
    try {
      if (category === 'all') {
        return await this.getAllImages()
      }

      const q = query(
        collection(db, 'gallery'),
        where('category', '==', category),
        orderBy('uploadedAt', 'desc')
      )
      const snapshot = await getDocs(q)

      const images = await Promise.all(
        snapshot.docs.map(async (docSnap) => {
          const data = docSnap.data()
          let imageUrl = data.imageUrl

          if (!imageUrl && data.imagePath) {
            try {
              const storageRef = ref(storage, data.imagePath)
              imageUrl = await getDownloadURL(storageRef)
            } catch (error) {
              console.warn('Could not get download URL for:', data.imagePath)
            }
          }

          return {
            id: docSnap.id,
            ...data,
            imageUrl
          } as GalleryImage
        })
      )

      return images
    } catch (error) {
      console.error('Error fetching images by category:', error)
      // Fallback to mock data
      const filtered = category === 'all' ? mockImages : mockImages.filter(img => img.category === category)
      return filtered
    }
  }

  // Get featured images
  static async getFeaturedImages(): Promise<GalleryImage[]> {
    try {
      const q = query(
        collection(db, 'gallery'),
        where('featured', '==', true),
        orderBy('uploadedAt', 'desc')
      )
      const snapshot = await getDocs(q)

      const images = await Promise.all(
        snapshot.docs.map(async (docSnap) => {
          const data = docSnap.data()
          let imageUrl = data.imageUrl

          if (!imageUrl && data.imagePath) {
            try {
              const storageRef = ref(storage, data.imagePath)
              imageUrl = await getDownloadURL(storageRef)
            } catch (error) {
              console.warn('Could not get download URL for:', data.imagePath)
            }
          }

          return {
            id: docSnap.id,
            ...data,
            imageUrl
          } as GalleryImage
        })
      )

      return images
    } catch (error) {
      console.error('Error fetching featured images:', error)
      // Fallback to mock data
      return mockImages.filter(img => img.featured)
    }
  }

  // Get single image by ID
  static async getImageById(id: string): Promise<GalleryImage | null> {
    // TODO: Replace with Firebase Firestore call
    // const doc = await db.collection('gallery').doc(id).get()
    // return doc.exists ? { id: doc.id, ...doc.data() } as GalleryImage : null
    
    return new Promise((resolve) => {
      setTimeout(() => {
        const image = mockImages.find(img => img.id === id)
        resolve(image || null)
      }, 200)
    })
  }

  // Get all categories
  static async getCategories(): Promise<GalleryCategory[]> {
    // TODO: Replace with Firebase Firestore call or compute from images
    return new Promise((resolve) => {
      setTimeout(() => resolve(mockCategories), 200)
    })
  }

  // Admin functions (for future admin dashboard)
  
  // Add new image
  static async addImage(imageData: Omit<GalleryImage, 'id' | 'uploadedAt'>): Promise<string> {
    // TODO: Replace with Firebase Firestore call
    // const docRef = await db.collection('gallery').add({
    //   ...imageData,
    //   uploadedAt: new Date().toISOString()
    // })
    // return docRef.id
    
    return new Promise((resolve) => {
      setTimeout(() => {
        const newId = Date.now().toString()
        resolve(newId)
      }, 500)
    })
  }

  // Update image
  static async updateImage(id: string, updates: Partial<GalleryImage>): Promise<void> {
    // TODO: Replace with Firebase Firestore call
    // await db.collection('gallery').doc(id).update(updates)
    
    return new Promise((resolve) => {
      setTimeout(() => resolve(), 300)
    })
  }

  // Delete image
  static async deleteImage(id: string): Promise<void> {
    // TODO: Replace with Firebase Firestore call
    // await db.collection('gallery').doc(id).delete()
    // Also delete from Firebase Storage if needed
    
    return new Promise((resolve) => {
      setTimeout(() => resolve(), 300)
    })
  }

  // Upload image to Firebase Storage
  static async uploadImage(file: File, path: string): Promise<string> {
    // TODO: Replace with Firebase Storage upload
    // const storageRef = storage.ref().child(path)
    // const snapshot = await storageRef.put(file)
    // return await snapshot.ref.getDownloadURL()
    
    return new Promise((resolve) => {
      setTimeout(() => {
        // Mock URL
        resolve(`https://firebasestorage.googleapis.com/mock/${path}`)
      }, 1000)
    })
  }

  // Search images
  static async searchImages(query: string): Promise<GalleryImage[]> {
    // TODO: Replace with Firebase Firestore search or Algolia
    return new Promise((resolve) => {
      setTimeout(() => {
        const filtered = mockImages.filter(img => 
          img.title.toLowerCase().includes(query.toLowerCase()) ||
          img.description.toLowerCase().includes(query.toLowerCase()) ||
          img.tags?.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
        )
        resolve(filtered)
      }, 400)
    })
  }
}

export default GalleryService
