'use client'

import { useState, useEffect } from 'react'
import { authService, AdminUser } from '@/lib/auth/authService'
import LoginForm from '@/components/auth/LoginForm'
import AdminDashboard from '@/components/dashboard/AdminDashboard'

export default function HomePage() {
  const [currentUser, setCurrentUser] = useState<AdminUser | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Listen to auth state changes
    const unsubscribe = authService.onAuthStateChanged((user) => {
      setCurrentUser(user)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [])

  const handleLogin = async (email: string, password: string) => {
    try {
      const user = await authService.signIn(email, password)
      setCurrentUser(user)
    } catch (error: any) {
      throw error
    }
  }

  const handleLogout = async () => {
    try {
      await authService.signOut()
      setCurrentUser(null)
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-cream via-ivory to-cream flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-4 border-royalBlue mx-auto mb-4"></div>
          <h2 className="text-2xl font-serif font-bold text-royalBlue">Loading Admin Dashboard...</h2>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-cream via-ivory to-cream">
      {currentUser ? (
        <AdminDashboard user={currentUser} onLogout={handleLogout} />
      ) : (
        <div className="min-h-screen flex items-center justify-center p-4">
          <div className="max-w-md w-full space-y-8">
            <div className="text-center">
              {/* Royal Logo */}
              <div className="mx-auto mb-6">
                <img
                  src="/website-images/logo.png"
                  alt="Adukrom Kingdom Logo"
                  className="h-24 w-auto mx-auto drop-shadow-lg"
                />
              </div>

              <h1 className="text-4xl font-serif font-bold bg-gradient-to-r from-royalBlue to-royalBlue/80 bg-clip-text text-transparent mb-2">
                Adukrom Kingdom
              </h1>
              <h2 className="text-2xl font-serif font-semibold text-royalBlue/80 mb-2">
                Admin Dashboard
              </h2>
              <div className="w-24 h-1 bg-gradient-to-r from-royalGold to-yellow-400 mx-auto mb-6"></div>
              <p className="text-gray-700 mb-8">
                Secure access for authorized administrators
              </p>
            </div>

            <LoginForm onLogin={handleLogin} />
          </div>
        </div>
      )}
    </div>
  )
}
