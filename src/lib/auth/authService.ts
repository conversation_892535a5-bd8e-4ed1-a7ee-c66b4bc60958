// Authentication service with role-based access control
import { 
  signInWithEmailAndPassword, 
  signOut, 
  onAuthStateChanged,
  User
} from 'firebase/auth'
import { doc, getDoc, setDoc } from 'firebase/firestore'
import { auth, db } from '../firebase/config'

export type UserRole = 'super_admin' | 'admin' | 'editor'

export interface AdminUser {
  uid: string
  email: string
  displayName?: string
  role: UserRole
  permissions: string[]
  createdAt: string
  lastLogin: string
  active: boolean
}

export interface RolePermissions {
  [key: string]: string[]
}

// Define permissions for each role
export const ROLE_PERMISSIONS: RolePermissions = {
  super_admin: [
    'users.manage',
    'content.create',
    'content.edit',
    'content.delete',
    'content.publish',
    'gallery.manage',
    'news.manage',
    'site.configure',
    'analytics.view',
    'system.admin'
  ],
  admin: [
    'content.create',
    'content.edit',
    'content.delete',
    'content.publish',
    'gallery.manage',
    'news.manage',
    'analytics.view'
  ],
  editor: [
    'content.create',
    'content.edit',
    'gallery.view',
    'news.create',
    'news.edit'
  ]
}

class AuthService {
  private currentUser: AdminUser | null = null

  // Sign in with email and password
  async signIn(email: string, password: string): Promise<AdminUser> {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password)
      const user = userCredential.user
      
      // Get user role and permissions from Firestore
      const adminUser = await this.getAdminUser(user.uid)
      
      if (!adminUser) {
        throw new Error('User is not authorized as an admin')
      }

      if (!adminUser.active) {
        throw new Error('User account is deactivated')
      }

      // Update last login
      await this.updateLastLogin(user.uid)
      
      this.currentUser = adminUser
      return adminUser
    } catch (error: any) {
      throw new Error(error.message || 'Authentication failed')
    }
  }

  // Sign out
  async signOut(): Promise<void> {
    try {
      await signOut(auth)
      this.currentUser = null
    } catch (error: any) {
      throw new Error(error.message || 'Sign out failed')
    }
  }

  // Get admin user data from Firestore
  async getAdminUser(uid: string): Promise<AdminUser | null> {
    try {
      const userDoc = await getDoc(doc(db, 'admin_users', uid))
      
      if (!userDoc.exists()) {
        return null
      }

      const userData = userDoc.data()
      return {
        uid,
        email: userData.email,
        displayName: userData.displayName,
        role: userData.role,
        permissions: ROLE_PERMISSIONS[userData.role] || [],
        createdAt: userData.createdAt,
        lastLogin: userData.lastLogin,
        active: userData.active
      }
    } catch (error) {
      console.error('Error getting admin user:', error)
      return null
    }
  }

  // Create admin user
  async createAdminUser(
    uid: string, 
    email: string, 
    role: UserRole, 
    displayName?: string
  ): Promise<void> {
    try {
      const adminUser = {
        email,
        displayName: displayName || '',
        role,
        createdAt: new Date().toISOString(),
        lastLogin: new Date().toISOString(),
        active: true
      }

      await setDoc(doc(db, 'admin_users', uid), adminUser)
    } catch (error) {
      console.error('Error creating admin user:', error)
      throw error
    }
  }

  // Update last login
  async updateLastLogin(uid: string): Promise<void> {
    try {
      await setDoc(
        doc(db, 'admin_users', uid),
        { lastLogin: new Date().toISOString() },
        { merge: true }
      )
    } catch (error) {
      console.error('Error updating last login:', error)
    }
  }

  // Check if user has permission
  hasPermission(permission: string): boolean {
    if (!this.currentUser) return false
    return this.currentUser.permissions.includes(permission)
  }

  // Check if user has role
  hasRole(role: UserRole): boolean {
    if (!this.currentUser) return false
    return this.currentUser.role === role
  }

  // Check if user has any of the specified roles
  hasAnyRole(roles: UserRole[]): boolean {
    if (!this.currentUser) return false
    return roles.includes(this.currentUser.role)
  }

  // Get current user
  getCurrentUser(): AdminUser | null {
    return this.currentUser
  }

  // Listen to auth state changes
  onAuthStateChanged(callback: (user: AdminUser | null) => void): () => void {
    return onAuthStateChanged(auth, async (firebaseUser: User | null) => {
      if (firebaseUser) {
        const adminUser = await this.getAdminUser(firebaseUser.uid)
        this.currentUser = adminUser
        callback(adminUser)
      } else {
        this.currentUser = null
        callback(null)
      }
    })
  }

  // Get user role display name
  getRoleDisplayName(role: UserRole): string {
    const roleNames = {
      super_admin: 'Super Admin',
      admin: 'Admin',
      editor: 'Editor'
    }
    return roleNames[role] || role
  }

  // Get role color for UI
  getRoleColor(role: UserRole): string {
    const roleColors = {
      super_admin: 'bg-purple-100 text-purple-800',
      admin: 'bg-blue-100 text-blue-800',
      editor: 'bg-green-100 text-green-800'
    }
    return roleColors[role] || 'bg-gray-100 text-gray-800'
  }
}

export const authService = new AuthService()
export default authService
