// Image configuration utility for managing image sources
// Supports local images and future Firebase integration

export type ImageSource = 'local' | 'firebase';

export interface ImageConfig {
  source: ImageSource;
  baseUrl: string;
  fallbackUrl?: string;
}

// Configuration object - modify this to switch between image sources
const IMAGE_CONFIG: ImageConfig = {
  source: (process.env.NEXT_PUBLIC_IMAGE_SOURCE as ImageSource) || 'local',
  baseUrl: process.env.NEXT_PUBLIC_IMAGE_BASE_URL || '/website-images',
  fallbackUrl: '/website-images' // Always fallback to local images
};

/**
 * Get the full image URL based on current configuration
 * @param imagePath - Relative path to the image (e.g., 'gallery/royal-palace.jpg')
 * @returns Full URL to the image
 */
export function getImageUrl(imagePath: string): string {
  const { source, baseUrl, fallbackUrl } = IMAGE_CONFIG;
  
  switch (source) {
    case 'firebase':
      // Future Firebase implementation
      // For now, return local path as fallback
      return `${fallbackUrl}/${imagePath}`;
    
    case 'local':
    default:
      return `${baseUrl}/${imagePath}`;
  }
}

/**
 * Get optimized image URL with size parameters
 * @param imagePath - Relative path to the image
 * @param width - Desired width (optional)
 * @param height - Desired height (optional)
 * @returns Optimized image URL
 */
export function getOptimizedImageUrl(
  imagePath: string, 
  width?: number, 
  height?: number
): string {
  const baseUrl = getImageUrl(imagePath);
  
  // For local images, return as-is (Next.js will handle optimization)
  if (IMAGE_CONFIG.source === 'local') {
    return baseUrl;
  }
  
  // Future Firebase/CDN optimization parameters can be added here
  return baseUrl;
}

/**
 * Check if image source is configured for external URLs
 * @returns boolean indicating if external source is being used
 */
export function isExternalImageSource(): boolean {
  return IMAGE_CONFIG.source !== 'local';
}

/**
 * Get image configuration for debugging
 * @returns Current image configuration
 */
export function getImageConfig(): ImageConfig {
  return IMAGE_CONFIG;
}
