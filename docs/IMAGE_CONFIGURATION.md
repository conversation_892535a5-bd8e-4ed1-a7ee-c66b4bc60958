# Image Configuration System

This document explains how to configure and manage images in the RoyalGallery component and other parts of the application.

## Overview

The image configuration system allows you to easily switch between different image sources without modifying component code. Currently supports:

- **Local images** (stored in `/public/website-images/`)
- **Firebase Storage** (future implementation)

## Configuration

### Environment Variables

Create a `.env.local` file in the project root with the following variables:

```env
# Image source: 'local' or 'firebase'
NEXT_PUBLIC_IMAGE_SOURCE=local

# Base URL for local images
NEXT_PUBLIC_IMAGE_BASE_URL=/website-images
```

### Switching Image Sources

#### Using Local Images (Default)
```env
NEXT_PUBLIC_IMAGE_SOURCE=local
NEXT_PUBLIC_IMAGE_BASE_URL=/website-images
```

#### Using Firebase Storage (Future)
```env
NEXT_PUBLIC_IMAGE_SOURCE=firebase
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
```

## Directory Structure

```
public/
└── website-images/
    └── gallery/
        ├── palace.jpg
        ├── kente-weaving.jpg
        ├── coronation1.jpg
        ├── traditional-dance.jpg
        ├── banquet-hall.jpg
        └── artifacts.jpg
```

## Usage in Components

### Import the utility functions
```typescript
import { getImageUrl, isExternalImageSource } from '../utils/imageConfig'
```

### Use in Image components
```typescript
<Image
  src={getImageUrl('gallery/palace.jpg')}
  alt="Royal Palace"
  fill
  unoptimized={!isExternalImageSource()}
/>
```

## API Reference

### `getImageUrl(imagePath: string): string`
Returns the full URL for an image based on current configuration.

### `isExternalImageSource(): boolean`
Returns true if using external image source (Firebase), false for local images.

### `getOptimizedImageUrl(imagePath: string, width?: number, height?: number): string`
Returns optimized image URL with size parameters (for future CDN integration).

## Adding New Images

1. Place image files in `/public/website-images/gallery/`
2. Update the component's image array with the new `imagePath`
3. No code changes needed when switching between local/Firebase sources

## Future Firebase Integration

When Firebase is implemented, only the environment variables need to change:

1. Set `NEXT_PUBLIC_IMAGE_SOURCE=firebase`
2. Add Firebase configuration variables
3. The `getImageUrl()` function will automatically handle Firebase URLs

## Troubleshooting

### Images not loading
- Check that image files exist in `/public/website-images/gallery/`
- Verify environment variables are set correctly
- Ensure image paths match exactly (case-sensitive)

### Next.js Image optimization issues
- Local images use `unoptimized={true}` by default
- External sources (Firebase) will use Next.js optimization
