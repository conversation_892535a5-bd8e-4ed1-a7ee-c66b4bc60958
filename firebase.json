{"firestore": {"database": "(default)", "location": "nam5", "rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}], "storage": {"rules": "storage.rules"}, "remoteconfig": {"template": "remoteconfig.template.json"}, "database": {"rules": "database.rules.json"}}